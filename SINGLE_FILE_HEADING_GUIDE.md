# 单文件按大标题分段指南

针对您希望在一个文件内按大标题用双换行符（\n\n）分段的需求，提供专门的解决方案。

## 功能特点

### 🎯 单文件分段
- **保持单一文件**: 不拆分成多个文件，所有内容在一个文件内
- **双换行分段**: 使用 `\n\n` 在大标题处分段
- **结构清晰**: 每个段落包含完整的章节内容
- **便于处理**: 适合后续批量处理和导入

### 📝 分段策略
- **一级标题**: 第X章、第X部分、X、 → 创建新段落
- **二级标题**: 第X节、X.、(X) → 创建新段落
- **三级以下**: 保持在同一段落内，不分段

## 使用方法

### 基础用法

```bash
# 单文件按大标题分段
python doc2txt.py "资产盘活政策.docx" --by-headings

# 指定输出文件
python doc2txt.py "政策文件.docx" --by-headings -o "政策文件_分段.txt"

# 批量处理文件夹
python doc2txt.py "D:\Documents\Temp\资产资源盘活相关政策汇编（docx版）" --by-headings
```

### 详细输出

```bash
# 显示分段统计信息
python doc2txt.py "政策文件.docx" --by-headings --verbose
```

## 输出格式

### 文件结构
```
政策文件_by_headings.txt
```

### 内容格式
```
第一章 总则

第一条 为了规范资产盘活工作，提高资产使用效率...
第二条 本办法适用于各级政府部门...


第二章 基本原则

第三条 资产盘活应当遵循以下原则...
第四条 坚持统筹规划，合理配置...


第三章 实施办法

第一节 组织架构

第五条 建立资产盘活工作领导小组...

第二节 工作流程

第六条 资产盘活工作按照以下流程进行...
```

## 针对您的具体需求

### 处理政策汇编文档

```bash
# 处理您的文档目录
python doc2txt.py "D:\Documents\Temp\资产资源盘活相关政策汇编（docx版）" --by-headings --verbose
```

### 预期输出
每个原始docx文件会生成一个对应的txt文件：
```
资产资源盘活相关政策汇编（docx版）/
├── 政策文件1_by_headings.txt
├── 政策文件2_by_headings.txt
├── 政策文件3_by_headings.txt
└── ...
```

### 分段效果示例

**原始文档**:
```
第一章总则第一条为了规范...第二条本办法适用...第二章基本原则第三条资产盘活...
```

**分段后**:
```
第一章 总则

第一条 为了规范资产盘活工作...
第二条 本办法适用于各级政府部门...


第二章 基本原则

第三条 资产盘活应当遵循以下原则...
```

## 导入RAGFlow建议

### 1. 文本预处理
分段后的文件可以直接导入RAGFlow，系统会自动：
- 按双换行符识别段落边界
- 保持每个段落的完整性
- 维护文档的逻辑结构

### 2. 分块策略
RAGFlow导入时建议设置：
- **分块方式**: 按段落分块
- **段落分隔符**: `\n\n`
- **最大块大小**: 根据段落长度调整
- **重叠设置**: 较小的重叠值（因为段落已经完整）

### 3. 检索优势
- **精确定位**: 查询结果能精确到具体章节
- **上下文完整**: 每个检索结果都是完整的章节内容
- **逻辑清晰**: 用户能够理解内容的来源和上下文

## 与其他功能结合

### 结合文本清理
```bash
# 先按标题分段
python doc2txt.py "政策文件.docx" --by-headings

# 再清理文本格式
python text_cleaner.py "政策文件_by_headings.txt"
```

### 结合RAG优化
```bash
# 按标题分段 + RAG优化
python doc2txt.py "政策文件.docx" --by-headings --rag
```

## 优势分析

### vs 多文件分段
| 特点 | 单文件分段 | 多文件分段 |
|------|-----------|-----------|
| 文件管理 | ✅ 简单 | ❌ 复杂 |
| 批量处理 | ✅ 容易 | ❌ 困难 |
| 导入便利 | ✅ 一次导入 | ❌ 多次导入 |
| 结构保持 | ✅ 完整保持 | ❌ 分散 |

### vs 固定大小分段
| 特点 | 按标题分段 | 固定大小分段 |
|------|-----------|-------------|
| 内容完整性 | ✅ 章节完整 | ❌ 可能截断 |
| 逻辑结构 | ✅ 保持结构 | ❌ 破坏结构 |
| 分段质量 | ✅ 语义边界 | ❌ 机械分割 |

## 实际使用场景

### 1. 政策文件处理
```bash
python doc2txt.py "政策汇编.docx" --by-headings
# 输出: 政策汇编_by_headings.txt
# 每个政策条款为一个段落
```

### 2. 技术文档处理
```bash
python doc2txt.py "技术手册.docx" --by-headings
# 输出: 技术手册_by_headings.txt
# 每个技术章节为一个段落
```

### 3. 法规文档处理
```bash
python doc2txt.py "法规条文.docx" --by-headings
# 输出: 法规条文_by_headings.txt
# 每个法条章节为一个段落
```

## 质量检查

### 分段效果验证
```bash
# 查看分段统计
python doc2txt.py "文档.docx" --by-headings --verbose

# 手动检查分段效果
cat "文档_by_headings.txt" | grep -c "^$"  # 统计空行数量
```

### 内容完整性检查
- 确认所有章节都被正确识别
- 验证段落边界是否合理
- 检查是否有内容丢失

## 注意事项

1. **标题格式**: 确保文档中的标题格式规范
2. **双换行**: 段落间使用双换行符分隔
3. **编码统一**: 输出文件使用UTF-8编码
4. **内容完整**: 每个段落包含完整的章节内容

这种单文件分段方式特别适合需要保持文档完整性，同时又要按逻辑结构分段的场景，是导入RAGFlow的理想格式！
