# 按大标题分段指南

针对您希望按照文档的大标题（章节）进行分段的需求，提供专门的解决方案。

## 功能特点

### 🎯 智能标题识别
- **一级标题**: 第一章、第二章、第一部分、第二部分、一、二、三、
- **二级标题**: 第一节、第二节、1.、2.、3.、(一)、(二)
- **自动过滤**: 忽略小标题，只按大标题（章节级别）分段

### 📁 输出结构
```
document_by_headings/
├── 01_第一章_概述.txt           # 第一章的完整内容
├── 02_第二章_基本原则.txt       # 第二章的完整内容
├── 03_第三章_实施办法.txt       # 第三章的完整内容
├── document_sections_metadata.json  # 章节元数据
└── document_contents.txt        # 文档目录摘要
```

## 使用方法

### 基础用法

```bash
# 单文件按大标题分段
python doc2txt.py "资产盘活政策.docx" --by-headings

# 批量处理文件夹
python doc2txt.py "D:\Documents\Temp\资产资源盘活相关政策汇编（docx版）" --by-headings

# 指定输出目录
python doc2txt.py "政策文件.docx" --by-headings -o "D:\Output\Sections"
```

### 高级用法

```bash
# 详细输出，显示每个章节信息
python doc2txt.py "政策文件.docx" --by-headings --verbose

# 结合文本优化
python doc2txt.py "政策文件.docx" --by-headings --no-processing  # 禁用文本处理
```

## 分段策略

### 标题级别判断
| 标题格式 | 级别 | 是否分段 | 示例 |
|----------|------|----------|------|
| 第X章 | 1级 | ✅ 分段 | 第一章 总则 |
| 第X节 | 2级 | ✅ 分段 | 第一节 基本原则 |
| X、 | 1级 | ✅ 分段 | 一、指导思想 |
| (X) | 2级 | ✅ 分段 | (一)总体要求 |
| X. | 2级 | ✅ 分段 | 1.基本原则 |
| (X) | 3级 | ❌ 不分段 | （一）具体措施 |
| X.X. | 3级 | ❌ 不分段 | 1.1.实施细则 |

### 智能合并规则
- **小标题不分段**: 3级以下标题不创建新文件，保持在同一章节内
- **内容完整性**: 确保每个章节包含完整的内容
- **层级保持**: 保留文档的层次结构信息

## 输出格式

### 章节文件格式
每个章节文件包含：

```markdown
# 第一章 总则

<!-- 章节级别: 1 -->
<!-- 来源: 资产盘活政策.docx -->

第一章 总则

第一条 为了规范资产盘活工作...

第二条 本办法适用于...

## 第一节 基本原则

第三条 资产盘活应当遵循以下原则...
```

### 元数据文件
`document_sections_metadata.json` 包含：

```json
{
  "source_file": "资产盘活政策.docx",
  "total_sections": 5,
  "sections": [
    {
      "index": 0,
      "title": "第一章 总则",
      "level": 1,
      "content_length": 1250,
      "start_line": 10
    }
  ]
}
```

### 目录摘要
`document_contents.txt` 包含：

```
# 文档目录

1. 第一章 总则 (1250 字符)
2. 第二章 基本原则 (980 字符)
  3. 第一节 指导思想 (450 字符)
  4. 第二节 基本要求 (530 字符)
5. 第三章 实施办法 (1800 字符)

总计: 5 个章节
```

## 针对您的具体需求

### 处理现有文档

```bash
# 处理您的政策汇编文档
python doc2txt.py "D:\Documents\Temp\资产资源盘活相关政策汇编（docx版）" --by-headings --verbose
```

这将：
1. 自动识别每个文档的章节结构
2. 按照大标题分割成独立文件
3. 保持每个章节的完整性
4. 生成清晰的文件命名

### 导入RAGFlow建议

1. **按章节导入**: 每个章节作为独立文档导入
2. **保留层级**: 利用文件名和元数据保持文档结构
3. **便于检索**: 用户查询时能精确定位到相关章节

## 优势对比

### 按大标题分段 vs 固定大小分段

| 特点 | 按大标题分段 | 固定大小分段 |
|------|-------------|-------------|
| 内容完整性 | ✅ 章节完整 | ❌ 可能截断 |
| 逻辑结构 | ✅ 保持结构 | ❌ 破坏结构 |
| 检索精度 | ✅ 精确到章节 | ❌ 模糊匹配 |
| 用户体验 | ✅ 符合阅读习惯 | ❌ 碎片化 |
| 文件管理 | ✅ 清晰命名 | ❌ 编号命名 |

## 实际效果示例

### 原始文档结构
```
资产盘活政策汇编.docx
├── 第一章 总则
│   ├── 第一条 目的依据
│   └── 第二条 适用范围
├── 第二章 基本原则
│   ├── 第一节 指导思想
│   └── 第二节 基本要求
└── 第三章 实施办法
    ├── 第一节 组织架构
    └── 第二节 工作流程
```

### 分段后文件
```
资产盘活政策汇编_by_headings/
├── 01_总则.txt                 # 包含第一章全部内容
├── 02_基本原则.txt             # 包含第二章全部内容
├── 03_实施办法.txt             # 包含第三章全部内容
├── 资产盘活政策汇编_sections_metadata.json
└── 资产盘活政策汇编_contents.txt
```

## 注意事项

1. **标题识别**: 确保文档中的标题格式规范
2. **内容完整**: 每个章节包含其下所有子内容
3. **文件命名**: 自动生成安全的文件名
4. **编码统一**: 所有输出文件使用UTF-8编码

## 与其他模式的结合

```bash
# 先按标题分段，再进行文本清理
python doc2txt.py "政策文件.docx" --by-headings
python text_cleaner.py "政策文件_by_headings/" --verbose

# 按标题分段后，每个章节再细分（如果需要）
python doc2txt.py "政策文件.docx" --by-headings
# 然后对大章节再次处理
python doc2txt.py "01_总则.txt" --chunk --chunk-size 1000
```

这种按大标题分段的方式特别适合政策文件、法规文档、技术手册等有清晰章节结构的文档，能够最大程度保持文档的逻辑完整性！
