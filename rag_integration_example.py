#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG集成示例
演示如何将doc2txt的输出集成到RAG系统中
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Any
import re

class RAGDocumentLoader:
    """
    加载doc2txt RAG格式输出的文档加载器
    """
    
    def __init__(self, rag_output_dir: str):
        self.rag_output_dir = Path(rag_output_dir)
        
    def load_documents(self) -> List[Dict[str, Any]]:
        """
        加载所有RAG格式的文档块
        
        Returns:
            包含文档内容和元数据的字典列表
        """
        documents = []
        
        # 查找所有chunk文件
        chunk_files = list(self.rag_output_dir.glob("*_chunk_*.txt"))
        chunk_files.sort()  # 确保顺序
        
        for chunk_file in chunk_files:
            try:
                doc = self.parse_chunk_file(chunk_file)
                if doc:
                    documents.append(doc)
            except Exception as e:
                print(f"解析文件 {chunk_file} 时出错: {e}")
                
        return documents
    
    def parse_chunk_file(self, chunk_file: Path) -> Dict[str, Any]:
        """
        解析单个chunk文件
        
        Args:
            chunk_file: chunk文件路径
            
        Returns:
            包含内容和元数据的字典
        """
        with open(chunk_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分离元数据和文本内容
        parts = content.split('---', 1)
        if len(parts) != 2:
            return None
            
        metadata_text, text_content = parts
        
        # 解析元数据
        metadata = {}
        for line in metadata_text.strip().split('\n'):
            if ':' in line:
                key, value = line.split(':', 1)
                metadata[key.strip()] = value.strip()
        
        return {
            'id': chunk_file.stem,
            'content': text_content.strip(),
            'metadata': metadata,
            'file_path': str(chunk_file)
        }
    
    def load_metadata(self) -> Dict[str, Any]:
        """
        加载元数据文件
        """
        metadata_files = list(self.rag_output_dir.glob("*_metadata.json"))
        if not metadata_files:
            return {}
            
        with open(metadata_files[0], 'r', encoding='utf-8') as f:
            return json.load(f)


class SimpleRAGSystem:
    """
    简单的RAG系统示例（不依赖外部库）
    """
    
    def __init__(self, documents: List[Dict[str, Any]]):
        self.documents = documents
        self.index = self.build_simple_index()
    
    def build_simple_index(self) -> Dict[str, List[int]]:
        """
        构建简单的关键词索引
        """
        index = {}
        
        for i, doc in enumerate(self.documents):
            content = doc['content'].lower()
            # 简单的分词（实际应用中应使用专业分词工具）
            words = re.findall(r'\w+', content)
            
            for word in words:
                if len(word) > 1:  # 忽略单字符
                    if word not in index:
                        index[word] = []
                    index[word].append(i)
        
        return index
    
    def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        简单的关键词搜索
        
        Args:
            query: 查询字符串
            top_k: 返回结果数量
            
        Returns:
            相关文档列表
        """
        query_words = re.findall(r'\w+', query.lower())
        doc_scores = {}
        
        for word in query_words:
            if word in self.index:
                for doc_idx in self.index[word]:
                    if doc_idx not in doc_scores:
                        doc_scores[doc_idx] = 0
                    doc_scores[doc_idx] += 1
        
        # 按分数排序
        sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)
        
        results = []
        for doc_idx, score in sorted_docs[:top_k]:
            doc = self.documents[doc_idx].copy()
            doc['relevance_score'] = score
            results.append(doc)
        
        return results
    
    def generate_context(self, query: str, max_context_length: int = 2000) -> str:
        """
        为查询生成上下文
        
        Args:
            query: 查询字符串
            max_context_length: 最大上下文长度
            
        Returns:
            组合的上下文字符串
        """
        relevant_docs = self.search(query, top_k=10)
        
        context_parts = []
        current_length = 0
        
        for doc in relevant_docs:
            content = doc['content']
            metadata = doc['metadata']
            
            # 添加来源信息
            source_info = f"[来源: {metadata.get('HIERARCHY', metadata.get('TITLE', '未知'))}]\n"
            full_content = source_info + content + "\n\n"
            
            if current_length + len(full_content) > max_context_length:
                break
                
            context_parts.append(full_content)
            current_length += len(full_content)
        
        return ''.join(context_parts)


def demo_rag_integration():
    """
    演示RAG集成的完整流程
    """
    print("=== RAG集成演示 ===\n")
    
    # 假设我们有一个RAG输出目录
    rag_dir = "./example_rag"  # 替换为实际的RAG输出目录
    
    if not os.path.exists(rag_dir):
        print(f"RAG输出目录不存在: {rag_dir}")
        print("请先运行: python doc2txt.py your_document.docx --rag")
        return
    
    # 1. 加载文档
    print("1. 加载RAG格式文档...")
    loader = RAGDocumentLoader(rag_dir)
    documents = loader.load_documents()
    metadata = loader.load_metadata()
    
    print(f"   加载了 {len(documents)} 个文档块")
    print(f"   来源文件: {metadata.get('source_file', '未知')}")
    print(f"   RAG配置: chunk_size={metadata.get('rag_config', {}).get('chunk_size', '未知')}")
    print()
    
    # 2. 构建RAG系统
    print("2. 构建RAG系统...")
    rag_system = SimpleRAGSystem(documents)
    print(f"   索引构建完成，包含 {len(rag_system.index)} 个关键词")
    print()
    
    # 3. 演示查询
    print("3. 演示查询...")
    queries = [
        "什么是人工智能",
        "如何实现机器学习",
        "深度学习的应用"
    ]
    
    for query in queries:
        print(f"查询: {query}")
        
        # 搜索相关文档
        results = rag_system.search(query, top_k=3)
        print(f"找到 {len(results)} 个相关文档块:")
        
        for i, result in enumerate(results, 1):
            metadata = result['metadata']
            print(f"  {i}. {metadata.get('HIERARCHY', metadata.get('TITLE', '未知'))}")
            print(f"     相关度: {result['relevance_score']}")
            print(f"     内容预览: {result['content'][:100]}...")
            print()
        
        # 生成上下文
        context = rag_system.generate_context(query, max_context_length=1000)
        print(f"生成的上下文 ({len(context)} 字符):")
        print(context[:500] + "..." if len(context) > 500 else context)
        print("-" * 50)
        print()


def export_for_external_rag(rag_dir: str, output_format: str = "jsonl"):
    """
    导出为外部RAG系统格式
    
    Args:
        rag_dir: RAG输出目录
        output_format: 输出格式 (jsonl, csv, parquet)
    """
    loader = RAGDocumentLoader(rag_dir)
    documents = loader.load_documents()
    
    if output_format == "jsonl":
        output_file = Path(rag_dir) / "documents.jsonl"
        with open(output_file, 'w', encoding='utf-8') as f:
            for doc in documents:
                json.dump(doc, f, ensure_ascii=False)
                f.write('\n')
        print(f"导出到: {output_file}")
    
    elif output_format == "csv":
        import csv
        output_file = Path(rag_dir) / "documents.csv"
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['id', 'content', 'source', 'title', 'hierarchy', 'chunk_type', 'chunk_index'])
            
            for doc in documents:
                metadata = doc['metadata']
                writer.writerow([
                    doc['id'],
                    doc['content'],
                    metadata.get('SOURCE', ''),
                    metadata.get('TITLE', ''),
                    metadata.get('HIERARCHY', ''),
                    metadata.get('CHUNK_TYPE', ''),
                    metadata.get('CHUNK_INDEX', '')
                ])
        print(f"导出到: {output_file}")


if __name__ == "__main__":
    # 运行演示
    demo_rag_integration()
    
    # 如果需要导出为其他格式，取消注释下面的行
    # export_for_external_rag("./example_rag", "jsonl")
