# 文本优化指南

针对您的文档目录 `D:\Documents\Temp\资产资源盘活相关政策汇编（docx版）\text` 中的文本质量问题，提供以下优化方案。

## 问题分析

### 常见问题
1. **字符间异常空格**: 中文字符之间有多余空格
2. **标点符号空格**: 标点符号前后有不必要的空格
3. **错误段落分割**: 句子被错误地分成多行
4. **格式标记残留**: 页码、页眉页脚等无用信息
5. **括号引号格式**: 括号和引号周围的空格问题

## 解决方案

### 方案1: 重新转换（推荐）
使用优化后的转换程序重新处理原始文档：

```bash
# 使用RAG模式重新转换（已优化文本清理）
python doc2txt.py "原始docx文件路径" --rag --rag-chunk-size 512

# 或使用普通模式重新转换
python doc2txt.py "原始docx文件路径" --chunk
```

### 方案2: 清理现有文本
使用专门的文本清理工具处理已转换的文本：

```bash
# 清理整个目录
python text_cleaner.py "D:\Documents\Temp\资产资源盘活相关政策汇编（docx版）\text"

# 清理单个文件
python text_cleaner.py "D:\Documents\Temp\资产资源盘活相关政策汇编（docx版）\text\某个文件.txt"

# 清理并保存到新位置
python text_cleaner.py "原文件.txt" -o "清理后文件.txt"
```

## 优化效果对比

### 优化前
```
资 产 盘 活 是 指 通 过 各 种 方 式 ， 提 高 资 产 的 使 用 效 率 。

（ 一 ） 盘 活 存 量 资 产
1 . 加 强 资 产 管 理
```

### 优化后
```
资产盘活是指通过各种方式，提高资产的使用效率。

（一）盘活存量资产
1. 加强资产管理
```

## 具体优化内容

### 1. 中文格式修复
- ✅ 移除中文字符间的异常空格
- ✅ 修复中文与标点符号间的空格
- ✅ 规范数字与中文单位的格式
- ✅ 修复小数点的空格问题

### 2. 标点符号优化
- ✅ 统一括号格式：`（一）`、`(1)`
- ✅ 规范引号格式：`"内容"`、`'内容'`
- ✅ 修复书名号：`《书名》`
- ✅ 清理重复标点：`。。。` → `。`

### 3. 段落结构修复
- ✅ 智能合并被错误分行的句子
- ✅ 保持标题和列表的独立性
- ✅ 确保句子结尾后有适当换行
- ✅ 规范段落间距

### 4. 无用内容清理
- ✅ 移除页码：`第1页`、`Page 1`、`1/10`
- ✅ 移除页眉页脚：`共10页第1页`
- ✅ 清理目录格式：`内容...........1`
- ✅ 移除分隔线：`----------`
- ✅ 清理孤立符号行

## 使用建议

### 对于您的具体情况

1. **备份原文件**
   ```bash
   # 先备份整个目录
   cp -r "D:\Documents\Temp\资产资源盘活相关政策汇编（docx版）\text" "D:\Documents\Temp\text_backup"
   ```

2. **批量清理**
   ```bash
   # 清理所有txt文件
   python text_cleaner.py "D:\Documents\Temp\资产资源盘活相关政策汇编（docx版）\text" --verbose
   ```

3. **检查效果**
   - 随机抽查几个文件查看清理效果
   - 确认重要内容没有丢失
   - 验证格式是否符合预期

4. **重新导入RAGFlow**
   - 删除RAGFlow中的旧文档
   - 重新上传清理后的文档
   - 测试检索效果

### 质量检查清单

- [ ] 中文字符间无异常空格
- [ ] 标点符号格式正确
- [ ] 段落结构合理
- [ ] 无页码等无用信息
- [ ] 标题格式统一
- [ ] 列表格式规范
- [ ] 句子完整不被截断

## 高级配置

### 自定义清理规则
如果需要针对特定格式问题进行清理，可以修改 `text_cleaner.py` 中的正则表达式：

```python
# 添加特定的清理规则
text = re.sub(r'特定模式', r'替换内容', text)
```

### 批处理脚本
创建批处理脚本自动化处理：

```bash
#!/bin/bash
# 批量处理脚本
for file in "D:\Documents\Temp\资产资源盘活相关政策汇编（docx版）\text"/*.txt; do
    echo "处理: $file"
    python text_cleaner.py "$file"
done
```

## 注意事项

1. **内容完整性**: 清理过程不会删除实际内容，只修复格式
2. **备份重要**: 处理前务必备份原文件
3. **逐步验证**: 建议先处理少量文件验证效果
4. **编码问题**: 确保文件使用UTF-8编码

## 效果评估

清理后的文本应该具备以下特点：
- 阅读流畅，无格式干扰
- 适合RAG系统索引和检索
- 保持原文档的逻辑结构
- 符合中文排版规范

通过这些优化，您的文档在RAGFlow中的检索效果和用户体验都会显著提升！
