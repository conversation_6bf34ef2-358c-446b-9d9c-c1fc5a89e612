# RAG系统优化指南

本文档提供了针对RAG（Retrieval-Augmented Generation）系统的最佳配置建议和使用方法。

## RAG模式特性

### 🎯 专门优化
- **智能文本分块**: 基于语义边界和文档结构
- **元数据丰富**: 包含层级、来源、类型等信息
- **重叠策略**: 确保上下文连续性
- **格式标准化**: 统一的输出格式便于索引

### 📊 推荐配置

#### 最佳文本块大小
```bash
# 中文文档推荐配置
python doc2txt.py document.docx --rag --rag-chunk-size 512 --rag-overlap 50

# 英文文档推荐配置  
python doc2txt.py document.docx --rag --rag-chunk-size 600 --rag-overlap 60

# 技术文档推荐配置
python doc2txt.py document.docx --rag --rag-chunk-size 800 --rag-overlap 80
```

#### 配置说明

| 参数 | 推荐值 | 说明 |
|------|--------|------|
| `rag-chunk-size` | 512-800 | 文本块大小（字符数） |
| `rag-overlap` | 50-80 | 块间重叠（字符数） |
| 最小块大小 | 100 | 避免过小的无意义块 |
| 最大块大小 | 1000 | 避免超出模型限制 |

## 使用方法

### 基础RAG转换
```bash
# 单文件转换
python doc2txt.py document.docx --rag

# 批量转换
python doc2txt.py /path/to/docs/ --rag

# 自定义配置
python doc2txt.py document.docx --rag --rag-chunk-size 600 --rag-overlap 60
```

### 输出结构
```
document_rag/
├── document_chunk_0001.txt    # 文本块1
├── document_chunk_0002.txt    # 文本块2
├── ...
├── document_metadata.json     # 元数据文件
└── document_summary.txt       # 文档结构摘要
```

### 文本块格式
每个文本块包含标准化的元数据头部：
```
SOURCE: /path/to/document.docx
TITLE: 第一章 概述
HIERARCHY: 第一章 > 第一节 > 基本概念
CHUNK_TYPE: section_part
CHUNK_INDEX: 0
---
这里是实际的文本内容...
```

## RAG系统集成建议

### 1. 向量数据库配置

#### Chroma
```python
import chromadb
from chromadb.config import Settings

client = chromadb.Client(Settings(
    chroma_db_impl="duckdb+parquet",
    persist_directory="./chroma_db"
))

collection = client.create_collection(
    name="documents",
    metadata={"hnsw:space": "cosine"}
)
```

#### Pinecone
```python
import pinecone

pinecone.init(
    api_key="your-api-key",
    environment="your-env"
)

index = pinecone.Index("documents")
# 推荐维度: 1536 (OpenAI ada-002)
```

### 2. 嵌入模型推荐

#### 中文文档
- **text-embedding-ada-002** (OpenAI)
- **m3e-base** (本地部署)
- **bge-large-zh** (本地部署)

#### 英文文档
- **text-embedding-ada-002** (OpenAI)
- **sentence-transformers/all-MiniLM-L6-v2**
- **sentence-transformers/all-mpnet-base-v2**

### 3. 检索配置

#### 相似度阈值
```python
# 推荐配置
similarity_threshold = 0.7  # 余弦相似度
top_k = 5  # 返回最相关的5个块
```

#### 重排序
```python
# 使用交叉编码器重排序
from sentence_transformers import CrossEncoder

reranker = CrossEncoder('cross-encoder/ms-marco-MiniLM-L-6-v2')
```

## 最佳实践

### 📝 文档预处理
1. **清理格式**: 移除页眉页脚、页码
2. **标准化标题**: 统一标题格式
3. **保留结构**: 维护文档层次关系
4. **元数据丰富**: 添加来源、类型等信息

### 🔍 检索优化
1. **混合检索**: 结合关键词和语义检索
2. **查询扩展**: 使用同义词和相关词
3. **上下文窗口**: 包含相邻文本块
4. **元数据过滤**: 基于文档类型、章节等过滤

### 🎯 生成优化
1. **上下文排序**: 按相关性排序检索结果
2. **长度控制**: 控制输入上下文长度
3. **引用标注**: 标明信息来源
4. **一致性检查**: 验证生成内容的一致性

## 性能调优

### 文本块大小影响

| 块大小 | 优点 | 缺点 | 适用场景 |
|--------|------|------|----------|
| 256-512 | 精确匹配，快速检索 | 可能丢失上下文 | 问答系统 |
| 512-800 | 平衡精度和上下文 | 中等计算成本 | 通用RAG |
| 800-1200 | 丰富上下文 | 检索精度下降 | 长文本生成 |

### 重叠策略

```python
# 句子级重叠（推荐）
overlap_type = "sentence"  # 保持句子完整性

# 固定字符重叠
overlap_type = "character"  # 简单但可能截断句子

# 语义重叠
overlap_type = "semantic"  # 基于语义相似度
```

## 常见问题解决

### Q: 检索结果不相关？
**A**: 调整参数
- 增加 `rag-chunk-size` 提供更多上下文
- 调整相似度阈值
- 使用更好的嵌入模型

### Q: 生成内容重复？
**A**: 优化重叠
- 减少 `rag-overlap` 值
- 使用语义去重
- 改进重排序算法

### Q: 处理速度慢？
**A**: 性能优化
- 减小文本块大小
- 使用更快的嵌入模型
- 优化向量数据库配置

## 集成示例

### LangChain集成
```python
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import Chroma
from langchain.embeddings import OpenAIEmbeddings

# 使用我们的RAG格式文本
def load_rag_documents(rag_dir):
    documents = []
    for chunk_file in Path(rag_dir).glob("*_chunk_*.txt"):
        with open(chunk_file, 'r', encoding='utf-8') as f:
            content = f.read()
            # 解析元数据和文本
            parts = content.split('---', 1)
            if len(parts) == 2:
                metadata_text, text = parts
                # 解析元数据...
                documents.append(Document(page_content=text, metadata=metadata))
    return documents
```

### LlamaIndex集成
```python
from llama_index import VectorStoreIndex, Document

def create_index_from_rag(rag_dir):
    documents = load_rag_documents(rag_dir)
    index = VectorStoreIndex.from_documents(documents)
    return index
```

## 监控和评估

### 关键指标
- **检索精度**: 相关文档的召回率
- **检索召回**: 所有相关文档的覆盖率
- **生成质量**: BLEU、ROUGE等指标
- **用户满意度**: 人工评估

### 评估工具
```python
# 使用RAGAS评估框架
from ragas import evaluate
from ragas.metrics import faithfulness, answer_relevancy

result = evaluate(
    dataset=eval_dataset,
    metrics=[faithfulness, answer_relevancy]
)
```

通过以上配置和最佳实践，您可以获得最佳的RAG系统性能！
