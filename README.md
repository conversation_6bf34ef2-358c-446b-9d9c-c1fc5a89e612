# Doc2Txt Converter

一个智能的DOC/DOCX转TXT工具，专为知识库上传优化，支持多级目录结构的智能分段。

## 功能特性

- 支持DOCX文件转换（使用python-docx库）
- 支持DOC文件转换（Windows上使用MS Word COM，其他平台使用LibreOffice）
- **智能识别文档标题层级**（章节、小节等）
- **基于标题结构进行智能分段**
- **文本预处理，优化知识库上传效果**
- 支持单个文件转换和批量目录转换
- 提取文档中的表格内容
- 跨平台支持（Windows、Linux、macOS）

## 安装依赖

### 基本依赖
```bash
pip install -r requirements.txt
```

### 平台特定依赖

#### Windows用户
如果您使用Windows并安装了Microsoft Word，程序会自动使用Word来转换DOC文件：
```bash
pip install pywin32
```

#### Linux/macOS用户
对于DOC文件转换，需要安装LibreOffice：

**Ubuntu/Debian:**
```bash
sudo apt-get install libreoffice
```

**macOS (使用Homebrew):**
```bash
brew install --cask libreoffice
```

**CentOS/RHEL:**
```bash
sudo yum install libreoffice
```

## 使用方法

### 基础转换

#### 转换单个文件
```bash
# 转换DOCX文件
python doc2txt.py document.docx

# 转换DOC文件
python doc2txt.py document.doc

# 指定输出文件名
python doc2txt.py document.docx -o output.txt
```

#### 批量转换目录
```bash
# 转换目录中所有DOC和DOCX文件
python doc2txt.py /path/to/documents/

# 转换到指定输出目录
python doc2txt.py /path/to/documents/ -o /path/to/output/

# 详细输出
python doc2txt.py /path/to/documents/ -v
```

### 知识库优化分段（推荐用于知识库上传）

#### 智能分段转换
```bash
# 基于标题层级的智能分段
python doc2txt.py document.docx --chunk

# 自定义分段大小（默认1500字符）
python doc2txt.py document.docx --chunk --chunk-size 2000

# 批量分段转换
python doc2txt.py /path/to/documents/ --chunk

# 禁用标题检测，使用简单分段
python doc2txt.py document.docx --chunk --no-headings

# 禁用文本预处理
python doc2txt.py document.docx --chunk --no-processing
```

#### 分段功能说明
程序能够识别以下标题格式并据此分段：

**中文格式：**
- `第一章`、`第二章` （一级标题）
- `第一节`、`第二节` （二级标题）
- `一、`、`二、`、`三、` （二级标题）
- `（一）`、`（二）` （三级标题）
- `1.`、`2.`、`3.` （二级标题）
- `1.1.`、`1.2.` （三级标题）

**英文格式：**
- `Chapter 1`、`Chapter 2` （一级标题）
- `Section 1`、`Section 2` （二级标题）

### Python代码中使用

```python
from doc2txt import Doc2TxtConverter

# 创建转换器实例
converter = Doc2TxtConverter()

# 转换单个文件
converter.convert_file('document.docx', 'output.txt')

# 批量转换目录
converted_files = converter.convert_directory('/path/to/docs/', '/path/to/output/')
```

## 支持的文件格式

- `.docx` - Microsoft Word 2007+格式
- `.doc` - Microsoft Word 97-2003格式

## 转换方法

### DOCX文件
使用`python-docx`库直接读取和解析DOCX文件内容，包括：
- 段落文本
- 表格内容（以制表符分隔）

### DOC文件
根据运行平台自动选择最佳转换方法：

1. **Windows + Microsoft Word**: 使用COM接口调用Word应用程序
2. **其他平台**: 使用LibreOffice的无头模式进行转换

## 错误处理

程序包含完善的错误处理机制：
- 文件不存在检查
- 依赖库检查
- 转换过程异常处理
- 超时保护（LibreOffice转换）

## 示例

### 基本使用示例
```bash
# 转换单个DOCX文件
python doc2txt.py example.docx

# 输出: Successfully converted: example.docx -> example.txt
```

### 批量转换示例
```bash
# 转换整个文档目录
python doc2txt.py ./documents/ -o ./txt_output/ -v

# 输出:
# Successfully converted: documents/file1.docx -> txt_output/file1.txt
# Successfully converted: documents/file2.doc -> txt_output/file2.txt
# Converted 2 files
```

## 注意事项

1. **DOC文件转换**：
   - Windows用户建议安装Microsoft Word以获得最佳转换效果
   - Linux/macOS用户需要安装LibreOffice

2. **编码**：输出的TXT文件使用UTF-8编码

3. **格式保留**：转换过程中会丢失原文档的格式信息（字体、颜色、样式等），只保留纯文本内容

4. **表格处理**：表格内容会被转换为制表符分隔的文本

## 故障排除

### 常见问题

**Q: 提示"python-docx library is required"**
A: 运行 `pip install python-docx`

**Q: DOC文件转换失败**
A: 
- Windows用户：确保安装了Microsoft Word，或安装LibreOffice
- Linux/macOS用户：安装LibreOffice

**Q: 转换后的文本乱码**
A: 程序使用UTF-8编码保存，确保您的文本编辑器支持UTF-8

## 许可证

MIT License
