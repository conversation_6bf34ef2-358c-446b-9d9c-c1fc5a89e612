#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Doc2Txt Converter
A Python program to convert DOC and DOCX files to TXT format.
"""

import os
import sys
import argparse
import re
from pathlib import Path
from typing import List, Optional, Dict

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import win32com.client
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

try:
    import subprocess
    SUBPROCESS_AVAILABLE = True
except ImportError:
    SUBPROCESS_AVAILABLE = False


class Doc2TxtConverter:
    """Convert DOC and DOCX files to TXT format."""

    def __init__(self, enable_text_processing: bool = True):
        self.supported_extensions = ['.doc', '.docx']
        self.enable_text_processing = enable_text_processing

    def process_text_for_knowledge_base(self, text: str) -> str:
        """
        Process extracted text to make it suitable for knowledge base upload.

        Args:
            text: Raw text extracted from document

        Returns:
            Processed text with improved formatting
        """
        if not self.enable_text_processing:
            return text

        # Remove excessive whitespace and normalize line breaks
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # Multiple empty lines -> double line break
        text = re.sub(r'[ \t]+', ' ', text)  # Multiple spaces/tabs -> single space
        text = re.sub(r'[ \t]*\n[ \t]*', '\n', text)  # Remove spaces around line breaks

        # Remove page numbers and headers/footers patterns
        text = re.sub(r'\n\s*\d+\s*\n', '\n', text)  # Standalone page numbers
        text = re.sub(r'\n\s*第\s*\d+\s*页\s*\n', '\n', text)  # Chinese page numbers
        text = re.sub(r'\n\s*Page\s+\d+\s*\n', '\n', text, flags=re.IGNORECASE)  # English page numbers

        # Clean up common document artifacts
        text = re.sub(r'\n\s*[-_=]{3,}\s*\n', '\n\n', text)  # Separator lines
        text = re.sub(r'\n\s*[*•·]{1,3}\s*\n', '\n\n', text)  # Bullet separators

        # Improve paragraph structure
        lines = text.split('\n')
        processed_lines = []

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                processed_lines.append('')
                continue

            # Detect potential headings (short lines, often capitalized or numbered)
            if (len(line) < 100 and
                (line.isupper() or
                 re.match(r'^[一二三四五六七八九十\d]+[、．.]', line) or
                 re.match(r'^[A-Z][a-z]*(\s+[A-Z][a-z]*)*$', line) or
                 re.match(r'^\d+\.?\d*\s', line))):
                # Add extra spacing around headings
                if processed_lines and processed_lines[-1]:
                    processed_lines.append('')
                processed_lines.append(line)
                processed_lines.append('')
            else:
                processed_lines.append(line)

        # Join and clean up final text
        processed_text = '\n'.join(processed_lines)
        processed_text = re.sub(r'\n{3,}', '\n\n', processed_text)  # Max 2 consecutive newlines
        processed_text = processed_text.strip()

        return processed_text

    def split_text_into_chunks(self, text: str, max_chunk_size: int = 1000, overlap: int = 100) -> List[str]:
        """
        Split text into chunks suitable for knowledge base ingestion.

        Args:
            text: Text to split
            max_chunk_size: Maximum characters per chunk
            overlap: Number of characters to overlap between chunks

        Returns:
            List of text chunks
        """
        if len(text) <= max_chunk_size:
            return [text]

        chunks = []
        paragraphs = text.split('\n\n')
        current_chunk = ""

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # If adding this paragraph would exceed chunk size
            if len(current_chunk) + len(paragraph) + 2 > max_chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())

                    # Start new chunk with overlap from previous chunk
                    if overlap > 0 and len(current_chunk) > overlap:
                        overlap_text = current_chunk[-overlap:].split('\n')[-1]
                        current_chunk = overlap_text + '\n\n' + paragraph
                    else:
                        current_chunk = paragraph
                else:
                    # Single paragraph is too long, split it by sentences
                    sentences = re.split(r'[。！？.!?]\s*', paragraph)
                    temp_chunk = ""

                    for sentence in sentences:
                        if not sentence.strip():
                            continue

                        sentence = sentence.strip() + '。' if not sentence.endswith(('.', '。', '!', '！', '?', '？')) else sentence.strip()

                        if len(temp_chunk) + len(sentence) + 1 > max_chunk_size:
                            if temp_chunk:
                                chunks.append(temp_chunk.strip())
                                temp_chunk = sentence
                            else:
                                # Even single sentence is too long, force split
                                chunks.append(sentence[:max_chunk_size])
                                temp_chunk = sentence[max_chunk_size:]
                        else:
                            temp_chunk = temp_chunk + ' ' + sentence if temp_chunk else sentence

                    if temp_chunk:
                        current_chunk = temp_chunk
            else:
                current_chunk = current_chunk + '\n\n' + paragraph if current_chunk else paragraph

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks
        
    def convert_docx_to_txt(self, docx_path: str, output_path: Optional[str] = None) -> str:
        """
        Convert DOCX file to TXT using python-docx library.
        
        Args:
            docx_path: Path to the DOCX file
            output_path: Optional output path for TXT file
            
        Returns:
            Path to the created TXT file
            
        Raises:
            ImportError: If python-docx is not installed
            FileNotFoundError: If input file doesn't exist
        """
        if not DOCX_AVAILABLE:
            raise ImportError("python-docx library is required for DOCX conversion. Install with: pip install python-docx")
            
        if not os.path.exists(docx_path):
            raise FileNotFoundError(f"File not found: {docx_path}")
            
        # Generate output path if not provided
        if output_path is None:
            output_path = str(Path(docx_path).with_suffix('.txt'))
            
        try:
            # Load the document
            doc = Document(docx_path)
            
            # Extract text from all paragraphs
            text_content = []
            for paragraph in doc.paragraphs:
                text_content.append(paragraph.text)
                
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        row_text.append(cell.text.strip())
                    text_content.append('\t'.join(row_text))
                    
            # Join all text with newlines
            full_text = '\n'.join(text_content)
            
            # Write to output file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(full_text)
                
            print(f"Successfully converted: {docx_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            raise Exception(f"Error converting DOCX file: {str(e)}")
    
    def convert_doc_to_txt_win32(self, doc_path: str, output_path: Optional[str] = None) -> str:
        """
        Convert DOC file to TXT using Win32 COM (Windows only).
        
        Args:
            doc_path: Path to the DOC file
            output_path: Optional output path for TXT file
            
        Returns:
            Path to the created TXT file
            
        Raises:
            ImportError: If pywin32 is not installed
            Exception: If conversion fails
        """
        if not WIN32_AVAILABLE:
            raise ImportError("pywin32 library is required for DOC conversion on Windows. Install with: pip install pywin32")
            
        if not os.path.exists(doc_path):
            raise FileNotFoundError(f"File not found: {doc_path}")
            
        # Generate output path if not provided
        if output_path is None:
            output_path = str(Path(doc_path).with_suffix('.txt'))
            
        try:
            # Create Word application object
            word = win32com.client.Dispatch("Word.Application")
            word.Visible = False
            
            # Open the document
            doc = word.Documents.Open(os.path.abspath(doc_path))
            
            # Save as TXT
            doc.SaveAs2(os.path.abspath(output_path), FileFormat=2)  # 2 = wdFormatText
            
            # Close document and quit Word
            doc.Close()
            word.Quit()
            
            print(f"Successfully converted: {doc_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            try:
                word.Quit()
            except:
                pass
            raise Exception(f"Error converting DOC file: {str(e)}")
    
    def convert_doc_to_txt_libreoffice(self, doc_path: str, output_path: Optional[str] = None) -> str:
        """
        Convert DOC file to TXT using LibreOffice (cross-platform).
        
        Args:
            doc_path: Path to the DOC file
            output_path: Optional output path for TXT file
            
        Returns:
            Path to the created TXT file
            
        Raises:
            Exception: If LibreOffice is not available or conversion fails
        """
        if not os.path.exists(doc_path):
            raise FileNotFoundError(f"File not found: {doc_path}")
            
        # Generate output path if not provided
        if output_path is None:
            output_path = str(Path(doc_path).with_suffix('.txt'))
            
        try:
            # Try to use LibreOffice headless mode
            cmd = [
                'libreoffice',
                '--headless',
                '--convert-to', 'txt',
                '--outdir', str(Path(output_path).parent),
                doc_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                raise Exception(f"LibreOffice conversion failed: {result.stderr}")
                
            print(f"Successfully converted: {doc_path} -> {output_path}")
            return output_path
            
        except FileNotFoundError:
            raise Exception("LibreOffice not found. Please install LibreOffice or use Windows with MS Word.")
        except subprocess.TimeoutExpired:
            raise Exception("LibreOffice conversion timed out.")
        except Exception as e:
            raise Exception(f"Error converting DOC file with LibreOffice: {str(e)}")
    
    def convert_file(self, input_path: str, output_path: Optional[str] = None) -> str:
        """
        Convert a single DOC or DOCX file to TXT.
        
        Args:
            input_path: Path to the input file
            output_path: Optional output path for TXT file
            
        Returns:
            Path to the created TXT file
        """
        file_ext = Path(input_path).suffix.lower()
        
        if file_ext not in self.supported_extensions:
            raise ValueError(f"Unsupported file extension: {file_ext}. Supported: {self.supported_extensions}")
        
        if file_ext == '.docx':
            return self.convert_docx_to_txt(input_path, output_path)
        elif file_ext == '.doc':
            # Try Win32 first (Windows), then LibreOffice
            try:
                return self.convert_doc_to_txt_win32(input_path, output_path)
            except ImportError:
                return self.convert_doc_to_txt_libreoffice(input_path, output_path)
    
    def convert_directory(self, input_dir: str, output_dir: Optional[str] = None) -> List[str]:
        """
        Convert all DOC and DOCX files in a directory.
        
        Args:
            input_dir: Path to the input directory
            output_dir: Optional output directory for TXT files
            
        Returns:
            List of paths to created TXT files
        """
        if not os.path.isdir(input_dir):
            raise NotADirectoryError(f"Directory not found: {input_dir}")
        
        if output_dir is None:
            output_dir = input_dir
        else:
            os.makedirs(output_dir, exist_ok=True)
        
        converted_files = []
        input_path = Path(input_dir)
        
        for file_path in input_path.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                try:
                    output_path = Path(output_dir) / file_path.with_suffix('.txt').name
                    result = self.convert_file(str(file_path), str(output_path))
                    converted_files.append(result)
                except Exception as e:
                    print(f"Error converting {file_path}: {str(e)}")
        
        return converted_files


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="Convert DOC and DOCX files to TXT format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python doc2txt.py file.docx                    # Convert single file
  python doc2txt.py file.doc -o output.txt       # Convert with custom output
  python doc2txt.py /path/to/docs/               # Convert all files in directory
  python doc2txt.py /path/to/docs/ -o /output/   # Convert directory with custom output
        """
    )
    
    parser.add_argument('input', help='Input file or directory path')
    parser.add_argument('-o', '--output', help='Output file or directory path')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    converter = Doc2TxtConverter()
    
    try:
        if os.path.isfile(args.input):
            # Convert single file
            result = converter.convert_file(args.input, args.output)
            if args.verbose:
                print(f"Conversion completed: {result}")
        elif os.path.isdir(args.input):
            # Convert directory
            results = converter.convert_directory(args.input, args.output)
            print(f"Converted {len(results)} files")
            if args.verbose:
                for result in results:
                    print(f"  {result}")
        else:
            print(f"Error: Input path does not exist: {args.input}")
            sys.exit(1)
            
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
