#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Doc2Txt Converter
A Python program to convert DOC and DOCX files to TXT format.
"""

import os
import sys
import argparse
import re
from pathlib import Path
from typing import List, Optional, Dict

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import win32com.client
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

try:
    import subprocess
    SUBPROCESS_AVAILABLE = True
except ImportError:
    SUBPROCESS_AVAILABLE = False


class Doc2TxtConverter:
    """Convert DOC and DOCX files to TXT format."""

    def __init__(self, enable_text_processing: bool = True, rag_mode: bool = False):
        self.supported_extensions = ['.doc', '.docx']
        self.enable_text_processing = enable_text_processing
        self.rag_mode = rag_mode

        # RAG优化配置
        self.rag_config = {
            'chunk_size': 512,  # RAG推荐的token数量（约400-600中文字符）
            'overlap': 50,      # 重叠token数量
            'min_chunk_size': 100,  # 最小块大小
            'max_chunk_size': 800,  # 最大块大小
            'sentence_splitters': ['。', '！', '？', '.', '!', '?', '；', ';'],
            'paragraph_separator': '\n\n',
            'section_separator': '\n---\n',
            'metadata_separator': '|||',
        }

    def process_text_for_knowledge_base(self, text: str) -> str:
        """
        Process extracted text to make it suitable for knowledge base upload.

        Args:
            text: Raw text extracted from document

        Returns:
            Processed text with improved formatting
        """
        if not self.enable_text_processing:
            return text

        # Remove excessive whitespace and normalize line breaks
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # Multiple empty lines -> double line break
        text = re.sub(r'[ \t]+', ' ', text)  # Multiple spaces/tabs -> single space
        text = re.sub(r'[ \t]*\n[ \t]*', '\n', text)  # Remove spaces around line breaks

        # Remove page numbers and headers/footers patterns
        text = re.sub(r'\n\s*\d+\s*\n', '\n', text)  # Standalone page numbers
        text = re.sub(r'\n\s*第\s*\d+\s*页\s*\n', '\n', text)  # Chinese page numbers
        text = re.sub(r'\n\s*Page\s+\d+\s*\n', '\n', text, flags=re.IGNORECASE)  # English page numbers

        # Clean up common document artifacts
        text = re.sub(r'\n\s*[-_=]{3,}\s*\n', '\n\n', text)  # Separator lines
        text = re.sub(r'\n\s*[*•·]{1,3}\s*\n', '\n\n', text)  # Bullet separators

        # Improve paragraph structure
        lines = text.split('\n')
        processed_lines = []

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                processed_lines.append('')
                continue

            # Detect potential headings (short lines, often capitalized or numbered)
            if (len(line) < 100 and
                (line.isupper() or
                 re.match(r'^[一二三四五六七八九十\d]+[、．.]', line) or
                 re.match(r'^[A-Z][a-z]*(\s+[A-Z][a-z]*)*$', line) or
                 re.match(r'^\d+\.?\d*\s', line))):
                # Add extra spacing around headings
                if processed_lines and processed_lines[-1]:
                    processed_lines.append('')
                processed_lines.append(line)
                processed_lines.append('')
            else:
                processed_lines.append(line)

        # Join and clean up final text
        processed_text = '\n'.join(processed_lines)
        processed_text = re.sub(r'\n{3,}', '\n\n', processed_text)  # Max 2 consecutive newlines
        processed_text = processed_text.strip()

        return processed_text

    def detect_heading_level(self, line: str) -> int:
        """
        Detect the heading level of a line based on common patterns.

        Args:
            line: Text line to analyze

        Returns:
            Heading level (1-6, 0 if not a heading)
        """
        line = line.strip()
        if not line:
            return 0

        # Chinese numbering patterns
        chinese_patterns = [
            (r'^第[一二三四五六七八九十百千万]+章\s', 1),  # 第一章
            (r'^第[一二三四五六七八九十百千万]+节\s', 2),  # 第一节
            (r'^[一二三四五六七八九十]+[、．.]\s', 2),     # 一、
            (r'^（[一二三四五六七八九十]+）\s', 3),        # （一）
            (r'^[1-9]\d*[、．.]\s', 2),                  # 1.
            (r'^[1-9]\d*\.[1-9]\d*[、．.]\s', 3),        # 1.1.
            (r'^[1-9]\d*\.[1-9]\d*\.[1-9]\d*[、．.]\s', 4), # 1.1.1.
        ]

        for pattern, level in chinese_patterns:
            if re.match(pattern, line):
                return level

        # English/Western patterns
        if re.match(r'^Chapter\s+\d+', line, re.IGNORECASE):
            return 1
        if re.match(r'^Section\s+\d+', line, re.IGNORECASE):
            return 2

        # All caps short lines (likely headings)
        if len(line) < 80 and line.isupper() and len(line.split()) <= 10:
            return 2

        # Title case short lines
        if (len(line) < 100 and
            re.match(r'^[A-Z][a-z]*(\s+[A-Z][a-z]*)*$', line) and
            len(line.split()) <= 15):
            return 3

        return 0

    def split_by_headings(self, text: str, max_chunk_size: int = 1500) -> List[Dict[str, str]]:
        """
        Split text into sections based on heading hierarchy.

        Args:
            text: Text to split
            max_chunk_size: Maximum characters per chunk

        Returns:
            List of dictionaries with 'title', 'content', and 'level' keys
        """
        lines = text.split('\n')
        sections = []
        current_section = {'title': '', 'content': '', 'level': 0}
        current_hierarchy = []  # Stack to track heading hierarchy

        for line in lines:
            heading_level = self.detect_heading_level(line)

            if heading_level > 0:
                # Save previous section if it has content
                if current_section['content'].strip():
                    sections.append(current_section.copy())

                # Update hierarchy stack
                while current_hierarchy and current_hierarchy[-1]['level'] >= heading_level:
                    current_hierarchy.pop()

                current_hierarchy.append({'title': line.strip(), 'level': heading_level})

                # Create hierarchical title
                title_parts = [h['title'] for h in current_hierarchy]
                hierarchical_title = ' > '.join(title_parts)

                # Start new section
                current_section = {
                    'title': hierarchical_title,
                    'content': line + '\n',
                    'level': heading_level
                }
            else:
                current_section['content'] += line + '\n'

        # Add the last section
        if current_section['content'].strip():
            sections.append(current_section)

        # Further split large sections
        final_sections = []
        for section in sections:
            if len(section['content']) <= max_chunk_size:
                final_sections.append(section)
            else:
                # Split large section into smaller chunks while preserving context
                chunks = self.split_large_section(section, max_chunk_size)
                final_sections.extend(chunks)

        return final_sections

    def split_large_section(self, section: Dict[str, str], max_chunk_size: int) -> List[Dict[str, str]]:
        """
        Split a large section into smaller chunks while preserving context.

        Args:
            section: Section dictionary with title and content
            max_chunk_size: Maximum characters per chunk

        Returns:
            List of smaller section chunks
        """
        content = section['content']
        title = section['title']
        level = section['level']

        # Split by paragraphs first
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]

        chunks = []
        current_chunk = ""
        chunk_index = 1

        for paragraph in paragraphs:
            if len(current_chunk) + len(paragraph) + 2 > max_chunk_size:
                if current_chunk:
                    chunk_title = f"{title} (Part {chunk_index})" if chunk_index > 1 else title
                    chunks.append({
                        'title': chunk_title,
                        'content': current_chunk.strip(),
                        'level': level
                    })
                    chunk_index += 1
                    current_chunk = paragraph
                else:
                    # Single paragraph too long, split by sentences
                    sentences = re.split(r'[。！？.!?]\s*', paragraph)
                    temp_chunk = ""

                    for sentence in sentences:
                        if not sentence.strip():
                            continue
                        sentence = sentence.strip()
                        if not sentence.endswith(('.', '。', '!', '！', '?', '？')):
                            sentence += '。'

                        if len(temp_chunk) + len(sentence) + 1 > max_chunk_size:
                            if temp_chunk:
                                chunk_title = f"{title} (Part {chunk_index})"
                                chunks.append({
                                    'title': chunk_title,
                                    'content': temp_chunk.strip(),
                                    'level': level
                                })
                                chunk_index += 1
                                temp_chunk = sentence
                            else:
                                # Force split very long sentence
                                chunk_title = f"{title} (Part {chunk_index})"
                                chunks.append({
                                    'title': chunk_title,
                                    'content': sentence[:max_chunk_size],
                                    'level': level
                                })
                                chunk_index += 1
                                temp_chunk = sentence[max_chunk_size:]
                        else:
                            temp_chunk = temp_chunk + ' ' + sentence if temp_chunk else sentence

                    if temp_chunk:
                        current_chunk = temp_chunk
            else:
                current_chunk = current_chunk + '\n\n' + paragraph if current_chunk else paragraph

        if current_chunk:
            chunk_title = f"{title} (Part {chunk_index})" if chunk_index > 1 else title
            chunks.append({
                'title': chunk_title,
                'content': current_chunk.strip(),
                'level': level
            })

        return chunks

    def split_text_into_chunks(self, text: str, max_chunk_size: int = 1000, overlap: int = 100, use_headings: bool = True) -> List[str]:
        """
        Split text into chunks suitable for knowledge base ingestion.

        Args:
            text: Text to split
            max_chunk_size: Maximum characters per chunk
            overlap: Number of characters to overlap between chunks
            use_headings: Whether to use heading-based splitting

        Returns:
            List of text chunks
        """
        if len(text) <= max_chunk_size:
            return [text]

        # Use heading-based splitting if enabled
        if use_headings:
            sections = self.split_by_headings(text, max_chunk_size)
            return [f"# {section['title']}\n\n{section['content']}" for section in sections]

        # Fallback to simple paragraph-based splitting
        chunks = []
        paragraphs = text.split('\n\n')
        current_chunk = ""

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # If adding this paragraph would exceed chunk size
            if len(current_chunk) + len(paragraph) + 2 > max_chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())

                    # Start new chunk with overlap from previous chunk
                    if overlap > 0 and len(current_chunk) > overlap:
                        overlap_text = current_chunk[-overlap:].split('\n')[-1]
                        current_chunk = overlap_text + '\n\n' + paragraph
                    else:
                        current_chunk = paragraph
                else:
                    # Single paragraph is too long, split it by sentences
                    sentences = re.split(r'[。！？.!?]\s*', paragraph)
                    temp_chunk = ""

                    for sentence in sentences:
                        if not sentence.strip():
                            continue

                        sentence = sentence.strip() + '。' if not sentence.endswith(('.', '。', '!', '！', '?', '？')) else sentence.strip()

                        if len(temp_chunk) + len(sentence) + 1 > max_chunk_size:
                            if temp_chunk:
                                chunks.append(temp_chunk.strip())
                                temp_chunk = sentence
                            else:
                                # Even single sentence is too long, force split
                                chunks.append(sentence[:max_chunk_size])
                                temp_chunk = sentence[max_chunk_size:]
                        else:
                            temp_chunk = temp_chunk + ' ' + sentence if temp_chunk else sentence

                    if temp_chunk:
                        current_chunk = temp_chunk
            else:
                current_chunk = current_chunk + '\n\n' + paragraph if current_chunk else paragraph

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    def process_text_for_rag(self, text: str, source_file: str = "") -> str:
        """
        专门为RAG系统优化的文本处理。

        Args:
            text: 原始文本
            source_file: 源文件路径（用于元数据）

        Returns:
            RAG优化后的文本
        """
        if not self.rag_mode:
            return self.process_text_for_knowledge_base(text)

        # 1. 基础清理和格式修复
        text = re.sub(r'\r\n|\r', '\n', text)  # 统一换行符

        # 修复中文字符间的异常空格
        text = re.sub(r'([\u4e00-\u9fff])\s+([\u4e00-\u9fff])', r'\1\2', text)  # 中文字符间空格
        text = re.sub(r'([\u4e00-\u9fff])\s+([，。！？；：、])', r'\1\2', text)  # 中文字符与标点间空格
        text = re.sub(r'([，。！？；：、])\s+([\u4e00-\u9fff])', r'\1\2', text)  # 标点与中文字符间空格

        # 修复数字和单位之间的空格问题
        text = re.sub(r'(\d+)\s*([%％万亿千百十元件个条项次年月日时分秒])', r'\1\2', text)
        text = re.sub(r'(\d+)\s*([.．])\s*(\d+)', r'\1\2\3', text)  # 修复小数点空格

        # 修复括号内外的空格
        text = re.sub(r'\s*([（(])\s*', r'\1', text)  # 左括号前后空格
        text = re.sub(r'\s*([）)])\s*', r'\1', text)  # 右括号前后空格
        text = re.sub(r'([（(])\s+', r'\1', text)  # 左括号后空格
        text = re.sub(r'\s+([）)])', r'\1', text)  # 右括号前空格

        # 修复引号的空格问题
        text = re.sub(r'\s*([""''「」『』])\s*', r'\1', text)

        # 规范化空格和换行
        text = re.sub(r'[ \t]+', ' ', text)  # 多个空格合并为一个
        text = re.sub(r'[ \t]*\n[ \t]*', '\n', text)  # 清理行首尾空格
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # 规范化段落间距

        # 2. 移除RAG不需要的内容
        # 移除页码和页眉页脚
        text = re.sub(r'\n\s*第?\s*\d+\s*页\s*\n', '\n', text)
        text = re.sub(r'\n\s*Page\s+\d+\s*\n', '\n', text, flags=re.IGNORECASE)
        text = re.sub(r'\n\s*\d+\s*/\s*\d+\s*\n', '\n', text)  # 页码格式 1/10
        text = re.sub(r'\n\s*-\s*\d+\s*-\s*\n', '\n', text)  # 页码格式 -1-

        # 移除目录页（连续的点号）
        text = re.sub(r'\n.*?\.{3,}.*?\d+\s*\n', '\n', text)
        text = re.sub(r'\n.*?…{2,}.*?\d+\s*\n', '\n', text)  # 省略号形式的目录

        # 移除常见的文档格式标记
        text = re.sub(r'\n\s*共\s*\d+\s*页\s*第\s*\d+\s*页\s*\n', '\n', text)
        text = re.sub(r'\n\s*第\s*\d+\s*页\s*共\s*\d+\s*页\s*\n', '\n', text)

        # 移除分隔线
        text = re.sub(r'\n\s*[-_=*]{3,}\s*\n', '\n\n', text)
        text = re.sub(r'\n\s*[━─]{3,}\s*\n', '\n\n', text)  # 中文分隔线

        # 移除重复的标点符号
        text = re.sub(r'([。！？]){2,}', r'\1', text)
        text = re.sub(r'([，；：]){2,}', r'\1', text)

        # 移除孤立的单个字符行（通常是格式错误）
        text = re.sub(r'\n\s*[^\w\u4e00-\u9fff]\s*\n', '\n', text)

        # 移除空白行过多的情况
        text = re.sub(r'\n{3,}', '\n\n', text)

        # 3. 标准化引用和列表格式
        # 统一列表标记
        text = re.sub(r'\n\s*[•·▪▫◦‣⁃]\s*', '\n• ', text)  # 统一bullet points
        text = re.sub(r'\n\s*[①②③④⑤⑥⑦⑧⑨⑩]\s*', lambda m: f'\n{ord(m.group(0)[-2]) - ord("①") + 1}. ', text)

        # 4. 优化标题格式（为RAG检索优化）
        lines = text.split('\n')
        processed_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                processed_lines.append('')
                continue

            # 检测并标准化标题
            heading_level = self.detect_heading_level(line)
            if heading_level > 0:
                # 为RAG系统添加标题标记
                if heading_level == 1:
                    line = f"# {line}"
                elif heading_level == 2:
                    line = f"## {line}"
                elif heading_level == 3:
                    line = f"### {line}"
                else:
                    line = f"#### {line}"

                # 确保标题前后有适当间距
                if processed_lines and processed_lines[-1]:
                    processed_lines.append('')
                processed_lines.append(line)
                processed_lines.append('')
            else:
                processed_lines.append(line)

        # 5. 最终清理
        processed_text = '\n'.join(processed_lines)
        processed_text = re.sub(r'\n{3,}', '\n\n', processed_text)
        processed_text = processed_text.strip()

        return processed_text

    def split_text_for_rag(self, text: str, source_file: str = "") -> List[Dict[str, str]]:
        """
        专门为RAG系统优化的文本分割。

        Args:
            text: 处理后的文本
            source_file: 源文件路径

        Returns:
            包含文本块和元数据的字典列表
        """
        chunks = []

        # 首先按标题分割
        sections = self.split_by_headings_for_rag(text)

        for section in sections:
            section_chunks = self.split_section_for_rag(section, source_file)
            chunks.extend(section_chunks)

        return chunks

    def split_by_headings_for_rag(self, text: str) -> List[Dict[str, str]]:
        """
        按标题分割文本，为RAG优化。
        """
        lines = text.split('\n')
        sections = []
        current_section = {'title': '', 'content': '', 'level': 0, 'hierarchy': []}
        hierarchy_stack = []

        for line in lines:
            line = line.strip()

            # 检测markdown格式的标题
            if re.match(r'^#{1,6}\s+', line):
                level = len(re.match(r'^#+', line).group())
                title = re.sub(r'^#+\s*', '', line)

                # 保存当前section
                if current_section['content'].strip():
                    sections.append(current_section.copy())

                # 更新层级栈
                while hierarchy_stack and hierarchy_stack[-1]['level'] >= level:
                    hierarchy_stack.pop()

                hierarchy_stack.append({'title': title, 'level': level})

                # 创建新section
                current_section = {
                    'title': title,
                    'content': line + '\n',
                    'level': level,
                    'hierarchy': [h['title'] for h in hierarchy_stack]
                }
            else:
                if line:
                    current_section['content'] += line + '\n'
                else:
                    current_section['content'] += '\n'

        # 添加最后一个section
        if current_section['content'].strip():
            sections.append(current_section)

        return sections

    def split_section_for_rag(self, section: Dict[str, str], source_file: str) -> List[Dict[str, str]]:
        """
        将section进一步分割为RAG适用的块。
        """
        content = section['content']
        title = section['title']
        hierarchy = section['hierarchy']

        # 如果内容较短，直接返回
        if len(content) <= self.rag_config['chunk_size']:
            return [{
                'text': content.strip(),
                'metadata': {
                    'source': source_file,
                    'title': title,
                    'hierarchy': ' > '.join(hierarchy),
                    'level': section['level'],
                    'chunk_type': 'complete_section',
                    'chunk_index': 0
                }
            }]

        # 分割长内容
        chunks = []
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]

        current_chunk = ""
        chunk_index = 0

        for paragraph in paragraphs:
            # 检查是否需要开始新块
            if (len(current_chunk) + len(paragraph) + 2 > self.rag_config['chunk_size'] and
                len(current_chunk) > self.rag_config['min_chunk_size']):

                # 保存当前块
                if current_chunk.strip():
                    chunks.append({
                        'text': current_chunk.strip(),
                        'metadata': {
                            'source': source_file,
                            'title': title,
                            'hierarchy': ' > '.join(hierarchy),
                            'level': section['level'],
                            'chunk_type': 'section_part',
                            'chunk_index': chunk_index
                        }
                    })
                    chunk_index += 1

                # 开始新块（带重叠）
                if self.rag_config['overlap'] > 0 and current_chunk:
                    # 取最后几句作为重叠
                    sentences = self.split_into_sentences(current_chunk)
                    overlap_sentences = sentences[-2:] if len(sentences) > 2 else sentences[-1:]
                    overlap_text = ''.join(overlap_sentences)
                    current_chunk = overlap_text + '\n\n' + paragraph
                else:
                    current_chunk = paragraph
            else:
                current_chunk = current_chunk + '\n\n' + paragraph if current_chunk else paragraph

        # 添加最后一块
        if current_chunk.strip():
            chunks.append({
                'text': current_chunk.strip(),
                'metadata': {
                    'source': source_file,
                    'title': title,
                    'hierarchy': ' > '.join(hierarchy),
                    'level': section['level'],
                    'chunk_type': 'section_part' if chunk_index > 0 else 'complete_section',
                    'chunk_index': chunk_index
                }
            })

        return chunks

    def split_into_sentences(self, text: str) -> List[str]:
        """
        将文本分割为句子。
        """
        # 使用正则表达式分割句子
        sentences = re.split(r'([。！？.!?])', text)

        # 重新组合句子和标点
        result = []
        for i in range(0, len(sentences) - 1, 2):
            if i + 1 < len(sentences):
                sentence = sentences[i] + sentences[i + 1]
                if sentence.strip():
                    result.append(sentence)

        return result

    def convert_file_for_rag(self, input_path: str, output_dir: Optional[str] = None) -> Dict[str, List[str]]:
        """
        专门为RAG系统转换文件。

        Args:
            input_path: 输入文件路径
            output_dir: 输出目录

        Returns:
            包含文件路径和元数据的字典
        """
        if output_dir is None:
            output_dir = str(Path(input_path).parent / f"{Path(input_path).stem}_rag")

        os.makedirs(output_dir, exist_ok=True)

        # 转换为文本
        temp_txt_path = str(Path(input_path).with_suffix('.txt'))
        self.convert_file(input_path, temp_txt_path)

        # 读取文本
        with open(temp_txt_path, 'r', encoding='utf-8') as f:
            raw_text = f.read()

        # RAG优化处理
        processed_text = self.process_text_for_rag(raw_text, input_path)

        # 分割为RAG块
        chunks = self.split_text_for_rag(processed_text, input_path)

        # 保存文件
        result = {
            'chunk_files': [],
            'metadata_file': '',
            'summary_file': ''
        }

        # 保存每个块
        for i, chunk in enumerate(chunks):
            chunk_filename = f"{Path(input_path).stem}_chunk_{i:04d}.txt"
            chunk_path = Path(output_dir) / chunk_filename

            # 构建块内容（包含元数据）
            chunk_content = self.format_chunk_for_rag(chunk)

            with open(chunk_path, 'w', encoding='utf-8') as f:
                f.write(chunk_content)

            result['chunk_files'].append(str(chunk_path))

        # 保存元数据文件
        metadata_path = Path(output_dir) / f"{Path(input_path).stem}_metadata.json"
        self.save_rag_metadata(chunks, str(metadata_path), input_path)
        result['metadata_file'] = str(metadata_path)

        # 保存摘要文件
        summary_path = Path(output_dir) / f"{Path(input_path).stem}_summary.txt"
        self.create_rag_summary(chunks, str(summary_path))
        result['summary_file'] = str(summary_path)

        # 清理临时文件
        if os.path.exists(temp_txt_path):
            os.remove(temp_txt_path)

        print(f"RAG转换完成: {len(chunks)} 个文本块")
        print(f"输出目录: {output_dir}")

        return result

    def format_chunk_for_rag(self, chunk: Dict[str, str]) -> str:
        """
        格式化文本块以适配RAG系统。
        """
        metadata = chunk['metadata']
        text = chunk['text']

        # 构建标准化的RAG格式
        formatted_content = []

        # 添加元数据头部
        formatted_content.append(f"SOURCE: {metadata['source']}")
        formatted_content.append(f"TITLE: {metadata['title']}")
        formatted_content.append(f"HIERARCHY: {metadata['hierarchy']}")
        formatted_content.append(f"CHUNK_TYPE: {metadata['chunk_type']}")
        formatted_content.append(f"CHUNK_INDEX: {metadata['chunk_index']}")
        formatted_content.append("---")

        # 添加文本内容
        formatted_content.append(text)

        return '\n'.join(formatted_content)

    def save_rag_metadata(self, chunks: List[Dict[str, str]], metadata_path: str, source_file: str):
        """
        保存RAG元数据到JSON文件。
        """
        import json

        metadata = {
            'source_file': source_file,
            'total_chunks': len(chunks),
            'rag_config': self.rag_config,
            'chunks': [
                {
                    'chunk_id': i,
                    'text_length': len(chunk['text']),
                    'metadata': chunk['metadata']
                }
                for i, chunk in enumerate(chunks)
            ]
        }

        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

    def create_rag_summary(self, chunks: List[Dict[str, str]], summary_path: str):
        """
        创建文档摘要，便于RAG系统理解文档结构。
        """
        summary_lines = []
        summary_lines.append("# 文档结构摘要")
        summary_lines.append("")

        # 按层级组织标题
        current_hierarchy = []
        for chunk in chunks:
            hierarchy = chunk['metadata']['hierarchy'].split(' > ')
            title = chunk['metadata']['title']

            # 检查层级变化
            if hierarchy != current_hierarchy:
                current_hierarchy = hierarchy
                level = len(hierarchy)
                indent = "  " * (level - 1)
                summary_lines.append(f"{indent}- {title}")

        summary_lines.append("")
        summary_lines.append(f"总计: {len(chunks)} 个文本块")
        summary_lines.append(f"平均块大小: {sum(len(c['text']) for c in chunks) // len(chunks)} 字符")

        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(summary_lines))

    def convert_file_with_chunking(self, input_path: str, output_dir: Optional[str] = None,
                                 max_chunk_size: int = 1500, use_headings: bool = True) -> List[str]:
        """
        Convert a file and split it into multiple chunk files for knowledge base.

        Args:
            input_path: Path to the input file
            output_dir: Directory to save chunk files
            max_chunk_size: Maximum characters per chunk
            use_headings: Whether to use heading-based splitting

        Returns:
            List of paths to created chunk files
        """
        if output_dir is None:
            output_dir = str(Path(input_path).parent / f"{Path(input_path).stem}_chunks")

        os.makedirs(output_dir, exist_ok=True)

        # First convert to text
        temp_txt_path = str(Path(input_path).with_suffix('.txt'))
        self.convert_file(input_path, temp_txt_path)

        # Read the converted text
        with open(temp_txt_path, 'r', encoding='utf-8') as f:
            text = f.read()

        # Split into chunks
        chunks = self.split_text_into_chunks(text, max_chunk_size, use_headings=use_headings)

        # Save each chunk as a separate file
        chunk_files = []
        for i, chunk in enumerate(chunks, 1):
            chunk_filename = f"{Path(input_path).stem}_chunk_{i:03d}.txt"
            chunk_path = Path(output_dir) / chunk_filename

            with open(chunk_path, 'w', encoding='utf-8') as f:
                f.write(chunk)

            chunk_files.append(str(chunk_path))
            print(f"Created chunk: {chunk_path}")

        # Clean up temporary file
        if os.path.exists(temp_txt_path):
            os.remove(temp_txt_path)

        return chunk_files
        
    def convert_docx_to_txt(self, docx_path: str, output_path: Optional[str] = None) -> str:
        """
        Convert DOCX file to TXT using python-docx library.
        
        Args:
            docx_path: Path to the DOCX file
            output_path: Optional output path for TXT file
            
        Returns:
            Path to the created TXT file
            
        Raises:
            ImportError: If python-docx is not installed
            FileNotFoundError: If input file doesn't exist
        """
        if not DOCX_AVAILABLE:
            raise ImportError("python-docx library is required for DOCX conversion. Install with: pip install python-docx")
            
        if not os.path.exists(docx_path):
            raise FileNotFoundError(f"File not found: {docx_path}")
            
        # Generate output path if not provided
        if output_path is None:
            output_path = str(Path(docx_path).with_suffix('.txt'))
            
        try:
            # Load the document
            doc = Document(docx_path)
            
            # Extract text from all paragraphs
            text_content = []
            for paragraph in doc.paragraphs:
                text_content.append(paragraph.text)
                
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        row_text.append(cell.text.strip())
                    text_content.append('\t'.join(row_text))
                    
            # Join all text with newlines
            full_text = '\n'.join(text_content)

            # Process text for knowledge base if enabled
            if self.enable_text_processing:
                full_text = self.process_text_for_knowledge_base(full_text)

            # Write to output file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(full_text)
                
            print(f"Successfully converted: {docx_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            raise Exception(f"Error converting DOCX file: {str(e)}")
    
    def convert_doc_to_txt_win32(self, doc_path: str, output_path: Optional[str] = None) -> str:
        """
        Convert DOC file to TXT using Win32 COM (Windows only).
        
        Args:
            doc_path: Path to the DOC file
            output_path: Optional output path for TXT file
            
        Returns:
            Path to the created TXT file
            
        Raises:
            ImportError: If pywin32 is not installed
            Exception: If conversion fails
        """
        if not WIN32_AVAILABLE:
            raise ImportError("pywin32 library is required for DOC conversion on Windows. Install with: pip install pywin32")
            
        if not os.path.exists(doc_path):
            raise FileNotFoundError(f"File not found: {doc_path}")
            
        # Generate output path if not provided
        if output_path is None:
            output_path = str(Path(doc_path).with_suffix('.txt'))
            
        try:
            # Create Word application object
            word = win32com.client.Dispatch("Word.Application")
            word.Visible = False
            
            # Open the document
            doc = word.Documents.Open(os.path.abspath(doc_path))
            
            # Save as TXT
            doc.SaveAs2(os.path.abspath(output_path), FileFormat=2)  # 2 = wdFormatText
            
            # Close document and quit Word
            doc.Close()
            word.Quit()
            
            print(f"Successfully converted: {doc_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            try:
                word.Quit()
            except:
                pass
            raise Exception(f"Error converting DOC file: {str(e)}")
    
    def convert_doc_to_txt_libreoffice(self, doc_path: str, output_path: Optional[str] = None) -> str:
        """
        Convert DOC file to TXT using LibreOffice (cross-platform).
        
        Args:
            doc_path: Path to the DOC file
            output_path: Optional output path for TXT file
            
        Returns:
            Path to the created TXT file
            
        Raises:
            Exception: If LibreOffice is not available or conversion fails
        """
        if not os.path.exists(doc_path):
            raise FileNotFoundError(f"File not found: {doc_path}")
            
        # Generate output path if not provided
        if output_path is None:
            output_path = str(Path(doc_path).with_suffix('.txt'))
            
        try:
            # Try to use LibreOffice headless mode
            cmd = [
                'libreoffice',
                '--headless',
                '--convert-to', 'txt',
                '--outdir', str(Path(output_path).parent),
                doc_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                raise Exception(f"LibreOffice conversion failed: {result.stderr}")
                
            print(f"Successfully converted: {doc_path} -> {output_path}")
            return output_path
            
        except FileNotFoundError:
            raise Exception("LibreOffice not found. Please install LibreOffice or use Windows with MS Word.")
        except subprocess.TimeoutExpired:
            raise Exception("LibreOffice conversion timed out.")
        except Exception as e:
            raise Exception(f"Error converting DOC file with LibreOffice: {str(e)}")
    
    def convert_file(self, input_path: str, output_path: Optional[str] = None) -> str:
        """
        Convert a single DOC or DOCX file to TXT.
        
        Args:
            input_path: Path to the input file
            output_path: Optional output path for TXT file
            
        Returns:
            Path to the created TXT file
        """
        file_ext = Path(input_path).suffix.lower()
        
        if file_ext not in self.supported_extensions:
            raise ValueError(f"Unsupported file extension: {file_ext}. Supported: {self.supported_extensions}")
        
        if file_ext == '.docx':
            return self.convert_docx_to_txt(input_path, output_path)
        elif file_ext == '.doc':
            # Try Win32 first (Windows), then LibreOffice
            try:
                return self.convert_doc_to_txt_win32(input_path, output_path)
            except ImportError:
                return self.convert_doc_to_txt_libreoffice(input_path, output_path)
    
    def convert_directory(self, input_dir: str, output_dir: Optional[str] = None) -> List[str]:
        """
        Convert all DOC and DOCX files in a directory.
        
        Args:
            input_dir: Path to the input directory
            output_dir: Optional output directory for TXT files
            
        Returns:
            List of paths to created TXT files
        """
        if not os.path.isdir(input_dir):
            raise NotADirectoryError(f"Directory not found: {input_dir}")
        
        if output_dir is None:
            output_dir = input_dir
        else:
            os.makedirs(output_dir, exist_ok=True)
        
        converted_files = []
        input_path = Path(input_dir)
        
        for file_path in input_path.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                try:
                    output_path = Path(output_dir) / file_path.with_suffix('.txt').name
                    result = self.convert_file(str(file_path), str(output_path))
                    converted_files.append(result)
                except Exception as e:
                    print(f"Error converting {file_path}: {str(e)}")
        
        return converted_files


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="Convert DOC and DOCX files to TXT format with intelligent chunking for knowledge bases",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python doc2txt.py file.docx                           # Convert single file
  python doc2txt.py file.doc -o output.txt              # Convert with custom output
  python doc2txt.py /path/to/docs/                      # Convert all files in directory
  python doc2txt.py /path/to/docs/ -o /output/          # Convert directory with custom output
  python doc2txt.py file.docx --chunk                   # Convert and split into chunks
  python doc2txt.py file.docx --chunk --chunk-size 2000 # Custom chunk size
  python doc2txt.py /docs/ --chunk --no-headings        # Chunk without heading detection
        """
    )

    parser.add_argument('input', help='Input file or directory path')
    parser.add_argument('-o', '--output', help='Output file or directory path')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output')
    parser.add_argument('--chunk', action='store_true',
                       help='Split output into chunks suitable for knowledge base')
    parser.add_argument('--chunk-size', type=int, default=1500,
                       help='Maximum characters per chunk (default: 1500)')
    parser.add_argument('--no-headings', action='store_true',
                       help='Disable heading-based chunking')
    parser.add_argument('--no-processing', action='store_true',
                       help='Disable text processing for knowledge base')
    parser.add_argument('--rag', action='store_true',
                       help='Enable RAG-optimized processing and output format')
    parser.add_argument('--rag-chunk-size', type=int, default=512,
                       help='RAG chunk size in characters (default: 512)')
    parser.add_argument('--rag-overlap', type=int, default=50,
                       help='RAG chunk overlap in characters (default: 50)')

    args = parser.parse_args()

    # 创建转换器实例
    converter = Doc2TxtConverter(
        enable_text_processing=not args.no_processing,
        rag_mode=args.rag
    )

    # 更新RAG配置
    if args.rag:
        converter.rag_config['chunk_size'] = args.rag_chunk_size
        converter.rag_config['overlap'] = args.rag_overlap

    try:
        if args.chunk:
            # Chunking mode
            if os.path.isfile(args.input):
                # Convert single file with chunking
                chunk_files = converter.convert_file_with_chunking(
                    args.input,
                    args.output,
                    args.chunk_size,
                    use_headings=not args.no_headings
                )
                print(f"Created {len(chunk_files)} chunk files")
                if args.verbose:
                    for chunk_file in chunk_files:
                        print(f"  {chunk_file}")
            elif os.path.isdir(args.input):
                # Convert directory with chunking
                input_path = Path(args.input)
                total_chunks = 0

                for file_path in input_path.rglob('*'):
                    if file_path.is_file() and file_path.suffix.lower() in converter.supported_extensions:
                        try:
                            output_dir = args.output if args.output else str(file_path.parent / f"{file_path.stem}_chunks")
                            chunk_files = converter.convert_file_with_chunking(
                                str(file_path),
                                output_dir,
                                args.chunk_size,
                                use_headings=not args.no_headings
                            )
                            total_chunks += len(chunk_files)
                            if args.verbose:
                                print(f"File: {file_path} -> {len(chunk_files)} chunks")
                        except Exception as e:
                            print(f"Error processing {file_path}: {str(e)}")

                print(f"Total chunks created: {total_chunks}")
            else:
                print(f"Error: Input path does not exist: {args.input}")
                sys.exit(1)
        else:
            # Regular conversion mode
            if os.path.isfile(args.input):
                # Convert single file
                result = converter.convert_file(args.input, args.output)
                if args.verbose:
                    print(f"Conversion completed: {result}")
            elif os.path.isdir(args.input):
                # Convert directory
                results = converter.convert_directory(args.input, args.output)
                print(f"Converted {len(results)} files")
                if args.verbose:
                    for result in results:
                        print(f"  {result}")
            else:
                print(f"Error: Input path does not exist: {args.input}")
                sys.exit(1)

    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
