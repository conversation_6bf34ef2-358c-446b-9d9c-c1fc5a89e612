#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本清理工具
专门用于清理和优化已转换的文本文件，修复常见的格式问题
"""

import os
import re
import argparse
from pathlib import Path
from typing import List

class TextCleaner:
    """
    文本清理器，专门处理中文文档的格式问题
    """
    
    def __init__(self):
        pass
    
    def clean_text(self, text: str) -> str:
        """
        清理文本中的格式问题
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        # 1. 基础格式修复
        text = self.fix_basic_formatting(text)
        
        # 2. 修复中文特有问题
        text = self.fix_chinese_formatting(text)
        
        # 3. 修复段落问题
        text = self.fix_paragraph_issues(text)
        
        # 4. 清理无用内容
        text = self.remove_unwanted_content(text)
        
        # 5. 最终整理
        text = self.final_cleanup(text)
        
        return text
    
    def fix_basic_formatting(self, text: str) -> str:
        """修复基础格式问题"""
        # 统一换行符
        text = re.sub(r'\r\n|\r', '\n', text)
        
        # 修复多余空格
        text = re.sub(r'[ \t]+', ' ', text)
        text = re.sub(r'[ \t]*\n[ \t]*', '\n', text)
        
        return text
    
    def fix_chinese_formatting(self, text: str) -> str:
        """修复中文文档特有的格式问题"""
        # 修复中文字符间的异常空格
        text = re.sub(r'([\u4e00-\u9fff])\s+([\u4e00-\u9fff])', r'\1\2', text)
        
        # 修复中文字符与标点间的空格
        text = re.sub(r'([\u4e00-\u9fff])\s+([，。！？；：、）】」』"])', r'\1\2', text)
        text = re.sub(r'([，。！？；：、（【「『"])\s+([\u4e00-\u9fff])', r'\1\2', text)
        
        # 修复数字和中文单位间的空格
        text = re.sub(r'(\d+)\s*([%％万亿千百十元件个条项次年月日时分秒公里米厘米毫米克千克吨])', r'\1\2', text)
        
        # 修复小数点的空格
        text = re.sub(r'(\d+)\s*([.．])\s*(\d+)', r'\1\2\3', text)
        
        # 修复括号的空格问题
        text = re.sub(r'\s*([（(])\s*', r'\1', text)
        text = re.sub(r'\s*([）)])\s*', r'\1', text)
        text = re.sub(r'([（(])\s+', r'\1', text)
        text = re.sub(r'\s+([）)])', r'\1', text)
        
        # 修复引号的空格问题
        text = re.sub(r'\s*([""''「」『』【】])\s*', r'\1', text)
        
        # 修复书名号的空格
        text = re.sub(r'\s*([《》〈〉])\s*', r'\1', text)
        
        return text
    
    def fix_paragraph_issues(self, text: str) -> str:
        """修复段落和句子的问题"""
        lines = text.split('\n')
        fixed_lines = []
        i = 0
        
        while i < len(lines):
            current_line = lines[i].strip()
            
            if not current_line:
                fixed_lines.append('')
                i += 1
                continue
            
            # 检查是否需要与下一行合并
            if (i + 1 < len(lines) and 
                not self.is_heading(current_line) and
                not re.search(r'[。！？：；]$', current_line) and
                len(current_line) < 120):  # 避免合并过长的行
                
                next_line = lines[i + 1].strip()
                
                if (next_line and 
                    not self.is_heading(next_line) and
                    not self.is_list_item(next_line) and
                    not re.match(r'^[一二三四五六七八九十\d]+[、．.]', next_line)):
                    
                    # 智能合并
                    if (re.search(r'[\u4e00-\u9fff]$', current_line) and 
                        re.search(r'^[\u4e00-\u9fff]', next_line)):
                        # 中文直接连接
                        fixed_lines.append(current_line + next_line)
                    else:
                        # 其他情况加空格
                        fixed_lines.append(current_line + ' ' + next_line)
                    i += 2
                    continue
            
            fixed_lines.append(current_line)
            i += 1
        
        text = '\n'.join(fixed_lines)
        
        # 修复错误的句子分割
        text = re.sub(r'([^\n。！？])\n([^\n\s#一二三四五六七八九十\d])', r'\1\2', text)
        
        # 确保句子结尾后有换行
        text = re.sub(r'([。！？])\s*([^\n\s])', r'\1\n\2', text)
        
        return text
    
    def remove_unwanted_content(self, text: str) -> str:
        """移除不需要的内容"""
        # 移除页码
        text = re.sub(r'\n\s*第?\s*\d+\s*页\s*\n', '\n', text)
        text = re.sub(r'\n\s*Page\s+\d+\s*\n', '\n', text, flags=re.IGNORECASE)
        text = re.sub(r'\n\s*\d+\s*/\s*\d+\s*\n', '\n', text)
        text = re.sub(r'\n\s*-\s*\d+\s*-\s*\n', '\n', text)
        
        # 移除页眉页脚格式
        text = re.sub(r'\n\s*共\s*\d+\s*页\s*第\s*\d+\s*页\s*\n', '\n', text)
        text = re.sub(r'\n\s*第\s*\d+\s*页\s*共\s*\d+\s*页\s*\n', '\n', text)
        
        # 移除目录页的点号
        text = re.sub(r'\n.*?\.{3,}.*?\d+\s*\n', '\n', text)
        text = re.sub(r'\n.*?…{2,}.*?\d+\s*\n', '\n', text)
        
        # 移除分隔线
        text = re.sub(r'\n\s*[-_=*━─]{3,}\s*\n', '\n\n', text)
        
        # 移除重复的标点
        text = re.sub(r'([。！？]){2,}', r'\1', text)
        text = re.sub(r'([，；：]){2,}', r'\1', text)
        
        # 移除孤立的符号行
        text = re.sub(r'\n\s*[^\w\u4e00-\u9fff\s]\s*\n', '\n', text)
        
        return text
    
    def final_cleanup(self, text: str) -> str:
        """最终清理"""
        # 规范化空行
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # 清理每行的首尾空格
        text = re.sub(r'^\s+|\s+$', '', text, flags=re.MULTILINE)
        
        # 清理文档首尾空白
        text = text.strip()
        
        return text
    
    def is_heading(self, line: str) -> bool:
        """判断是否为标题"""
        line = line.strip()
        if not line:
            return False
        
        # 检查常见的标题格式
        patterns = [
            r'^第[一二三四五六七八九十百千万]+章',
            r'^第[一二三四五六七八九十百千万]+节',
            r'^[一二三四五六七八九十]+[、．.]',
            r'^（[一二三四五六七八九十]+）',
            r'^[1-9]\d*[、．.]',
            r'^[1-9]\d*\.[1-9]\d*[、．.]',
            r'^#{1,6}\s+',  # Markdown格式
        ]
        
        for pattern in patterns:
            if re.match(pattern, line):
                return True
        
        # 检查全大写短行
        if len(line) < 50 and line.isupper():
            return True
        
        return False
    
    def is_list_item(self, line: str) -> bool:
        """判断是否为列表项"""
        line = line.strip()
        patterns = [
            r'^[•·▪▫◦‣⁃]\s',
            r'^[①②③④⑤⑥⑦⑧⑨⑩]\s',
            r'^\d+[.．]\s',
            r'^[a-zA-Z][.．]\s',
        ]
        
        for pattern in patterns:
            if re.match(pattern, line):
                return True
        
        return False
    
    def clean_file(self, input_path: str, output_path: str = None) -> str:
        """
        清理单个文件
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径（如果为None，则覆盖原文件）
            
        Returns:
            输出文件路径
        """
        if output_path is None:
            output_path = input_path
        
        # 读取文件
        with open(input_path, 'r', encoding='utf-8') as f:
            text = f.read()
        
        # 清理文本
        cleaned_text = self.clean_text(text)
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_text)
        
        return output_path
    
    def clean_directory(self, input_dir: str, pattern: str = "*.txt") -> List[str]:
        """
        批量清理目录中的文件
        
        Args:
            input_dir: 输入目录
            pattern: 文件匹配模式
            
        Returns:
            处理的文件列表
        """
        input_path = Path(input_dir)
        processed_files = []
        
        for file_path in input_path.rglob(pattern):
            if file_path.is_file():
                try:
                    self.clean_file(str(file_path))
                    processed_files.append(str(file_path))
                    print(f"已清理: {file_path}")
                except Exception as e:
                    print(f"清理失败 {file_path}: {e}")
        
        return processed_files


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="文本清理工具 - 修复中文文档的格式问题",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python text_cleaner.py file.txt                    # 清理单个文件
  python text_cleaner.py file.txt -o cleaned.txt     # 清理并保存到新文件
  python text_cleaner.py /path/to/text/              # 批量清理目录
  python text_cleaner.py /path/to/text/ --pattern "*.txt"  # 指定文件模式
        """
    )
    
    parser.add_argument('input', help='输入文件或目录路径')
    parser.add_argument('-o', '--output', help='输出文件路径（仅对单文件有效）')
    parser.add_argument('--pattern', default='*.txt', help='文件匹配模式（默认: *.txt）')
    parser.add_argument('-v', '--verbose', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    cleaner = TextCleaner()
    
    try:
        if os.path.isfile(args.input):
            # 清理单个文件
            result = cleaner.clean_file(args.input, args.output)
            print(f"文件清理完成: {result}")
        elif os.path.isdir(args.input):
            # 批量清理
            results = cleaner.clean_directory(args.input, args.pattern)
            print(f"批量清理完成，处理了 {len(results)} 个文件")
            if args.verbose:
                for result in results:
                    print(f"  {result}")
        else:
            print(f"错误: 路径不存在: {args.input}")
            return 1
            
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
