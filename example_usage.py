#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Doc2Txt使用示例
演示各种转换模式的使用方法
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from doc2txt import Doc2TxtConverter

def example_basic_conversion():
    """基础转换示例"""
    print("=== 基础转换示例 ===")
    
    converter = Doc2TxtConverter()
    
    # 假设有一个测试文件
    test_file = "test_document.docx"
    if not os.path.exists(test_file):
        print(f"测试文件 {test_file} 不存在，跳过基础转换示例")
        return
    
    try:
        # 转换单个文件
        output_file = converter.convert_file(test_file)
        print(f"转换完成: {test_file} -> {output_file}")
        
        # 读取并显示部分内容
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"文件大小: {len(content)} 字符")
            print(f"内容预览: {content[:200]}...")
            
    except Exception as e:
        print(f"转换失败: {e}")
    
    print()

def example_chunking_conversion():
    """分段转换示例"""
    print("=== 分段转换示例 ===")
    
    converter = Doc2TxtConverter()
    
    test_file = "test_document.docx"
    if not os.path.exists(test_file):
        print(f"测试文件 {test_file} 不存在，跳过分段转换示例")
        return
    
    try:
        # 分段转换
        chunk_files = converter.convert_file_with_chunking(
            test_file,
            max_chunk_size=1000,
            use_headings=True
        )
        
        print(f"分段转换完成，生成 {len(chunk_files)} 个文件:")
        for chunk_file in chunk_files:
            print(f"  {chunk_file}")
            
    except Exception as e:
        print(f"分段转换失败: {e}")
    
    print()

def example_rag_conversion():
    """RAG优化转换示例"""
    print("=== RAG优化转换示例 ===")
    
    # 启用RAG模式
    converter = Doc2TxtConverter(rag_mode=True)
    
    test_file = "test_document.docx"
    if not os.path.exists(test_file):
        print(f"测试文件 {test_file} 不存在，跳过RAG转换示例")
        return
    
    try:
        # RAG转换
        result = converter.convert_file_for_rag(test_file)
        
        print(f"RAG转换完成:")
        print(f"  文本块数量: {len(result['chunk_files'])}")
        print(f"  元数据文件: {result['metadata_file']}")
        print(f"  摘要文件: {result['summary_file']}")
        
        # 显示第一个文本块的内容
        if result['chunk_files']:
            first_chunk = result['chunk_files'][0]
            with open(first_chunk, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"\n第一个文本块内容:")
                print(content[:500] + "..." if len(content) > 500 else content)
                
    except Exception as e:
        print(f"RAG转换失败: {e}")
    
    print()

def example_batch_conversion():
    """批量转换示例"""
    print("=== 批量转换示例 ===")
    
    converter = Doc2TxtConverter()
    
    # 创建测试目录结构
    test_dir = "test_documents"
    if not os.path.exists(test_dir):
        print(f"测试目录 {test_dir} 不存在，跳过批量转换示例")
        return
    
    try:
        # 批量转换
        converted_files = converter.convert_directory(test_dir)
        
        print(f"批量转换完成，处理了 {len(converted_files)} 个文件:")
        for converted_file in converted_files:
            print(f"  {converted_file}")
            
    except Exception as e:
        print(f"批量转换失败: {e}")
    
    print()

def example_custom_processing():
    """自定义处理示例"""
    print("=== 自定义处理示例 ===")
    
    # 创建自定义配置的转换器
    converter = Doc2TxtConverter(rag_mode=True)
    
    # 自定义RAG配置
    converter.rag_config.update({
        'chunk_size': 800,
        'overlap': 80,
        'min_chunk_size': 150,
        'max_chunk_size': 1200
    })
    
    test_text = """
    # 第一章 人工智能概述
    
    人工智能（Artificial Intelligence，AI）是计算机科学的一个分支。
    
    ## 1.1 定义
    
    人工智能是指由人制造出来的机器所表现出来的智能。
    
    ## 1.2 发展历史
    
    人工智能的发展可以追溯到20世纪50年代。
    
    # 第二章 机器学习
    
    机器学习是人工智能的一个重要分支。
    """
    
    try:
        # 处理文本
        processed_text = converter.process_text_for_rag(test_text, "example.txt")
        print("处理后的文本:")
        print(processed_text)
        print()
        
        # 分割为RAG块
        chunks = converter.split_text_for_rag(processed_text, "example.txt")
        print(f"分割为 {len(chunks)} 个RAG块:")
        
        for i, chunk in enumerate(chunks):
            print(f"\n块 {i+1}:")
            print(f"  标题: {chunk['metadata']['title']}")
            print(f"  层级: {chunk['metadata']['hierarchy']}")
            print(f"  类型: {chunk['metadata']['chunk_type']}")
            print(f"  内容长度: {len(chunk['text'])} 字符")
            print(f"  内容预览: {chunk['text'][:100]}...")
            
    except Exception as e:
        print(f"自定义处理失败: {e}")
    
    print()

def create_test_files():
    """创建测试文件（如果不存在）"""
    print("=== 创建测试文件 ===")
    
    # 这里只是示例，实际使用时您需要准备真实的DOC/DOCX文件
    test_files = [
        "test_document.docx",
        "test_documents/doc1.docx",
        "test_documents/doc2.doc"
    ]
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            print(f"测试文件 {test_file} 不存在")
            print("请准备一些DOC/DOCX文件用于测试")
    
    print("提示: 将您的DOC/DOCX文件重命名为 test_document.docx 来运行示例")
    print()

def main():
    """主函数"""
    print("Doc2Txt 使用示例")
    print("=" * 50)
    print()
    
    # 检查测试文件
    create_test_files()
    
    # 运行各种示例
    example_basic_conversion()
    example_chunking_conversion()
    example_rag_conversion()
    example_batch_conversion()
    example_custom_processing()
    
    print("=" * 50)
    print("示例运行完成！")
    print()
    print("命令行使用方法:")
    print("  基础转换: python doc2txt.py document.docx")
    print("  分段转换: python doc2txt.py document.docx --chunk")
    print("  RAG转换:  python doc2txt.py document.docx --rag")
    print("  批量转换: python doc2txt.py /path/to/docs/ --rag")
    print()
    print("更多信息请查看 RAG_GUIDE.md")

if __name__ == "__main__":
    main()
