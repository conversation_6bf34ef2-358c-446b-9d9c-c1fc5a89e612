#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按大标题分段功能
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from doc2txt import Doc2TxtConverter

def test_heading_detection():
    """测试标题检测功能"""
    print("=== 测试标题检测功能 ===")
    
    converter = Doc2TxtConverter()
    
    test_lines = [
        "第一章 总则",
        "第二章 基本原则", 
        "第一节 指导思想",
        "第二节 基本要求",
        "一、总体要求",
        "二、具体措施",
        "(一)组织架构",
        "(二)工作流程",
        "1. 基本原则",
        "2. 实施办法",
        "（一）具体措施",
        "1.1. 详细规定",
        "普通文本内容",
        "这是一个普通段落"
    ]
    
    for line in test_lines:
        level = converter.detect_heading_level(line)
        print(f"'{line}' -> 级别: {level}")
    
    print()

def test_single_file_segmentation():
    """测试单文件分段功能"""
    print("=== 测试单文件分段功能 ===")
    
    converter = Doc2TxtConverter()
    
    # 模拟文档内容
    test_text = """
第一章 总则

第一条 为了规范资产盘活工作，提高资产使用效率，根据相关法律法规，制定本办法。

第二条 本办法适用于各级政府部门、事业单位和国有企业的资产盘活工作。

第二章 基本原则

第三条 资产盘活应当遵循以下原则：
（一）统筹规划，合理配置；
（二）公开透明，规范操作；
（三）提高效率，节约成本。

第一节 指导思想

第四条 坚持以习近平新时代中国特色社会主义思想为指导。

第二节 基本要求

第五条 建立健全资产盘活工作机制。

第三章 实施办法

第六条 资产盘活工作按照以下流程进行：
1. 资产清查
2. 评估论证
3. 方案制定
4. 组织实施

第一节 组织架构

第七条 建立资产盘活工作领导小组。

第二节 工作流程

第八条 按照规定程序开展工作。
"""
    
    # 测试分段
    segmented_text = converter.segment_by_major_headings_single_file(test_text)
    
    print("分段结果:")
    print("-" * 50)
    print(segmented_text)
    print("-" * 50)
    
    # 统计段落数
    segments = segmented_text.split('\n\n')
    segments = [s.strip() for s in segments if s.strip()]
    print(f"总段落数: {len(segments)}")
    
    for i, segment in enumerate(segments, 1):
        first_line = segment.split('\n')[0]
        print(f"段落{i}: {first_line}")
    
    print()

def test_file_conversion():
    """测试文件转换功能"""
    print("=== 测试文件转换功能 ===")
    
    # 创建测试文本文件
    test_file = "test_document.txt"
    test_content = """第一章 总则

第一条 这是第一条内容。

第二条 这是第二条内容。

第二章 基本原则

第三条 这是第三条内容。

第一节 具体要求

第四条 这是第四条内容。

第二节 实施细则

第五条 这是第五条内容。

第三章 附则

第六条 这是第六条内容。"""
    
    # 写入测试文件
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    converter = Doc2TxtConverter()
    
    # 测试分段
    segmented_text = converter.segment_by_major_headings_single_file(test_content)
    
    # 保存结果
    output_file = "test_document_by_headings.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(segmented_text)
    
    print(f"测试文件已创建: {test_file}")
    print(f"分段结果已保存: {output_file}")
    
    # 显示结果
    segments = segmented_text.split('\n\n')
    segments = [s.strip() for s in segments if s.strip()]
    print(f"分段数量: {len(segments)}")
    
    for i, segment in enumerate(segments, 1):
        lines = segment.split('\n')
        first_line = lines[0] if lines else ""
        content_lines = len(lines)
        print(f"  段落{i}: {first_line} ({content_lines} 行)")
    
    print()

def main():
    """主函数"""
    print("按大标题分段功能测试")
    print("=" * 50)
    print()
    
    test_heading_detection()
    test_single_file_segmentation()
    test_file_conversion()
    
    print("=" * 50)
    print("测试完成！")
    print()
    print("使用方法:")
    print("  python doc2txt.py document.docx --by-headings")
    print("  python doc2txt.py /path/to/docs/ --by-headings --verbose")

if __name__ == "__main__":
    main()
